{"page": {"getStarted": "Get started by editing", "saveChanges": "Save and see your changes instantly.", "deployNow": "Deploy now", "readDocs": "Read our docs", "learn": "Learn", "examples": "Examples", "goToNextjs": "Go to nextjs.org →"}, "layout": {"title": "THIqah COnsultant Helper", "description": "Your trusted consultant helper"}, "auth": {"common": {"email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Full Name", "forgotPassword": "Forgot Password?", "rememberMe": "Remember me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "or": "OR", "continueWithGoogle": "Continue with Google", "passwordRequirements": "Password must be at least 8 characters", "passwordMismatch": "Passwords do not match", "invalidEmail": "Please enter a valid email address", "requiredField": "This field is required", "signOut": "Sign Out"}, "signin": {"title": "Sign In", "subtitle": "Welcome back! Please enter your details", "button": "Sign In", "success": "Signed in successfully", "error": "Invalid email or password"}, "signup": {"title": "Sign Up", "subtitle": "Create an account to get started", "button": "Sign Up", "success": "Account created successfully", "error": "Error creating account", "termsAndConditions": "By signing up, you agree to our Terms and Conditions"}, "verify": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "We've sent a verification email to your inbox", "button": "<PERSON><PERSON><PERSON>", "resendEmail": "Didn't receive the email? <PERSON>sen<PERSON>", "success": "Email verified successfully", "error": "Error verifying email", "checkInbox": "Please check your inbox and click the verification link. After verifying, click the button below to continue."}, "reset": {"title": "Reset Password", "subtitle": "Enter your email to reset your password", "button": "Reset Password", "backToSignIn": "Back to Sign In", "success": "Password reset email sent", "error": "Error sending password reset email"}, "success": {"title": "Login Successful", "subtitle": "Welcome to your account", "accountInfo": "Account Information", "userId": "User ID", "emailVerified": "<PERSON><PERSON>", "emailVerifiedYes": "Yes", "emailVerifiedNo": "No", "role": "Role", "joined": "Joined", "verifyEmailButton": "<PERSON><PERSON><PERSON>", "signOutButton": "Sign Out", "signOutSuccess": "Signed out successfully", "signOutError": "Error signing out"}}, "ai": {"nameMeaning": {"title": "Discover Your Name's Meaning", "description": "Curious about the story behind the name \"{{name}}\"? Let's find out!", "buttonDefault": "Find Meaning", "buttonLoading": "Thinking...", "loading": "Generating meaning", "errorTitle": "AI Error", "errorPrefix": "Sorry, an error occurred", "errorGeneric": "Something went wrong while getting the name meaning.", "errorRateLimit": "You've asked too many times recently. Please wait a minute.", "errorServer": "The AI service is currently unavailable. Please try again later.", "errorNetwork": "Could not connect to the AI service. Please check your connection."}, "muscleTraining": {"title": "Muscle Training Generator", "description": "Get personalized workout routines for any muscle group", "inputPlaceholder": "Enter muscle name (e.g., biceps, chest, legs)", "generateButton": "Generate", "loading": "Creating workout plan", "errorTitle": "AI Error", "errorPrefix": "Sorry, an error occurred", "errorGeneric": "Something went wrong while generating the workout routine.", "errorRateLimit": "You've asked too many times recently. Please wait a minute.", "errorServer": "The AI service is currently unavailable. Please try again later.", "errorNetwork": "Could not connect to the AI service. Please check your connection.", "inputRequired": "Muscle Required", "pleaseEnterMuscle": "Please enter a muscle name to generate a workout routine."}, "imageDescription": {"title": "AI Image Describer", "description": "Upload an image and get a detailed description generated by AI.", "uploadArea": "Select an image file", "uploadPrompt": "Click or tap here to select an image", "buttonDefault": "Describe Image", "buttonLoading": "Describing...", "loading": "Analyzing image and generating description", "errorTitle": "AI Error", "errorPrefix": "Sorry, an error occurred", "errorGeneric": "Something went wrong while describing the image.", "errorRateLimit": "You've described too many images recently. Please wait a minute.", "errorServer": "The AI service is currently unavailable. Please try again later.", "errorNetwork": "Could not connect to the AI service. Please check your connection.", "errorFileSize": "The selected image file is too large (Max 10MB).", "errorInvalidFileType": "Invalid file type. Please select an image (JPEG, PNG, GIF, WEBP, etc.)."}}}