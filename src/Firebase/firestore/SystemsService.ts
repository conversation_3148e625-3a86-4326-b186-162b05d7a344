import {
  collection,
  doc,
  addDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp,
  writeBatch,
  getDoc
} from 'firebase/firestore';
import { firestore as db } from './firestoreConfig';

export interface System {
  id?: string;
  name: string;
  responsibleOwner: string;
  dba: string;
  email: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface SystemData {
  id?: string;
  schemaName?: string;
  tableName: string;
  columnName: string;
  dataType: string;
  maxLength?: number | null;
  isNullable?: boolean | null;
  columnOrder?: number | null;
  lastSeek?: string | null;
  lastScan?: string | null;
  lastLookup?: string | null;
  lastUpdate?: string | null;
  importOrder?: number;
  createdAt: Timestamp;
  systemId: string;
  // AI Classification attributes
  tableType?: "system_table" | "data_table";
  dataCategory?: "customers" | "development_team";
  confidentialityLevel?: "Public" | "Confidential" | "Secret" | "Top Secret";
  confidentialityReasoning?: string;
  hasPersonalData?: boolean;
  personalDataReason?: string;
  personalDataReasoning?: string;
  classificationStatus?: "pending" | "table_classified" | "fully_classified";
}

export interface SystemContext {
  id?: string;
  context: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  systemId: string;
}

const COLLECTION_NAME = 'systems';
const SYSTEM_DATA_SUBCOLLECTION = 'systemData';
const SYSTEM_CONTEXT_SUBCOLLECTION = 'systemContext';

export class SystemsService {
  // Add a new system
  static async addSystem(systemData: Omit<System, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const docRef = await addDoc(collection(db, COLLECTION_NAME), {
        ...systemData,
        createdAt: now,
        updatedAt: now,
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding system:', error);
      throw new Error('Failed to add system');
    }
  }

  // Get all systems
  static async getSystems(): Promise<System[]> {
    try {
      const q = query(collection(db, COLLECTION_NAME), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as System[];
    } catch (error) {
      console.error('Error fetching systems:', error);
      throw new Error('Failed to fetch systems');
    }
  }

  // Update a system
  static async updateSystem(id: string, systemData: Partial<Omit<System, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await updateDoc(docRef, {
        ...systemData,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating system:', error);
      throw new Error('Failed to update system');
    }
  }

  // Delete a system
  static async deleteSystem(id: string): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting system:', error);
      throw new Error('Failed to delete system');
    }
  }

  // Add system data in batches
  static async addSystemDataBatch(systemId: string, systemDataArray: Omit<SystemData, 'id' | 'createdAt' | 'systemId'>[]): Promise<void> {
    try {
      const batch = writeBatch(db);
      const now = Timestamp.now();
      
      systemDataArray.forEach((data) => {
        const systemDataRef = doc(collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION));
        batch.set(systemDataRef, {
          ...data,
          systemId,
          createdAt: now,
        });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error adding system data batch:', error);
      throw new Error('Failed to add system data batch');
    }
  }

  // Get system data
  static async getSystemData(systemId: string, limitCount?: number): Promise<SystemData[]> {
    try {
      const systemDataCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION);
      
      // Get all data first, then sort in memory to handle missing importOrder fields
      const querySnapshot = await getDocs(systemDataCollection);
      
      let systemData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SystemData[];

      // Sort by importOrder (ascending), fallback to createdAt for legacy data
      systemData.sort((a, b) => {
        if (a.importOrder !== undefined && b.importOrder !== undefined) {
          return a.importOrder - b.importOrder;
        }
        if (a.importOrder !== undefined && b.importOrder === undefined) {
          return -1; // New data with importOrder comes first
        }
        if (a.importOrder === undefined && b.importOrder !== undefined) {
          return 1; // Old data without importOrder comes last
        }
        // Both are legacy data, sort by createdAt
        return a.createdAt.toMillis() - b.createdAt.toMillis();
      });

      // Apply limit if specified
      if (limitCount) {
        systemData = systemData.slice(0, limitCount);
      }
      
      return systemData;
    } catch (error) {
      console.error('Error fetching system data:', error);
      throw new Error('Failed to fetch system data');
    }
  }

  // Get system data count
  static async getSystemDataCount(systemId: string): Promise<number> {
    try {
      const systemDataCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION);
      const querySnapshot = await getDocs(systemDataCollection);
      return querySnapshot.size;
    } catch (error) {
      console.error('Error fetching system data count:', error);
      return 0;
    }
  }

  // Delete all system data
  static async deleteAllSystemData(systemId: string): Promise<void> {
    try {
      const systemDataCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION);
      const querySnapshot = await getDocs(systemDataCollection);

      const batch = writeBatch(db);
      querySnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error deleting system data:', error);
      throw new Error('Failed to delete system data');
    }
  }

  // Update individual system data document with classification attributes
  static async updateSystemDataDocument(
    systemId: string,
    documentId: string,
    updates: Partial<Pick<SystemData, 'tableType' | 'dataCategory' | 'confidentialityLevel' | 'confidentialityReasoning' | 'hasPersonalData' | 'personalDataReason' | 'personalDataReasoning' | 'classificationStatus'>>
  ): Promise<void> {
    try {
      // Remove undefined values to avoid Firebase errors
      const cleanUpdates = Object.fromEntries(
        Object.entries(updates).filter(([_, value]) => value !== undefined)
      );

      const docRef = doc(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION, documentId);

      // Check if document exists first
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        console.warn(`Document ${documentId} does not exist, cannot update`);
        throw new Error(`Document ${documentId} not found`);
      }

      await updateDoc(docRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating system data document:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        throw error; // Re-throw document not found errors
      }
      throw new Error('Failed to update system data document');
    }
  }

  // Update multiple system data documents in batch
  static async updateSystemDataBatch(
    systemId: string,
    updates: Array<{
      documentId: string;
      data: Partial<Pick<SystemData, 'tableType' | 'dataCategory' | 'confidentialityLevel' | 'confidentialityReasoning' | 'hasPersonalData' | 'personalDataReason' | 'personalDataReasoning' | 'classificationStatus'>>;
    }>
  ): Promise<void> {
    try {
      const batch = writeBatch(db);
      const failedUpdates: string[] = [];

      for (const { documentId, data } of updates) {
        try {
          // Remove undefined values to avoid Firebase errors
          const cleanData = Object.fromEntries(
            Object.entries(data).filter(([_, value]) => value !== undefined)
          );

          const docRef = doc(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION, documentId);

          // Check if document exists first
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            // Document exists, use update
            batch.update(docRef, cleanData);
          } else {
            // Document doesn't exist, skip this update and log it
            console.warn(`Document ${documentId} does not exist, skipping update`);
            failedUpdates.push(documentId);
          }
        } catch (docError) {
          console.error(`Error preparing update for document ${documentId}:`, docError);
          failedUpdates.push(documentId);
        }
      }

      // Only commit if there are valid updates
      if (updates.length > failedUpdates.length) {
        await batch.commit();
      }

      // Log any failed updates
      if (failedUpdates.length > 0) {
        console.warn(`Failed to update ${failedUpdates.length} documents:`, failedUpdates);
      }

    } catch (error) {
      console.error('Error updating system data batch:', error);
      throw new Error('Failed to update system data batch');
    }
  }

  // Save system context
  static async saveSystemContext(systemId: string, context: string): Promise<void> {
    try {
      const now = Timestamp.now();
      const contextCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_CONTEXT_SUBCOLLECTION);
      
      // Check if context already exists
      const existingContext = await getDocs(contextCollection);
      
      if (existingContext.empty) {
        // Create new context
        await addDoc(contextCollection, {
          context,
          systemId,
          createdAt: now,
          updatedAt: now,
        });
      } else {
        // Update existing context (should only be one)
        const contextDoc = existingContext.docs[0];
        await updateDoc(contextDoc.ref, {
          context,
          updatedAt: now,
        });
      }
    } catch (error) {
      console.error('Error saving system context:', error);
      throw new Error('Failed to save system context');
    }
  }

  // Get system context
  static async getSystemContext(systemId: string): Promise<SystemContext | null> {
    try {
      const contextCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_CONTEXT_SUBCOLLECTION);
      const querySnapshot = await getDocs(contextCollection);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      const contextDoc = querySnapshot.docs[0];
      return {
        id: contextDoc.id,
        ...contextDoc.data()
      } as SystemContext;
    } catch (error) {
      console.error('Error fetching system context:', error);
      return null;
    }
  }
} 