import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  getDocs,
  query,
  where, 
  CollectionReference,
  DocumentData,
  addDoc,
  updateDoc,
  deleteDoc,
  writeBatch,
  serverTimestamp
} from "firebase/firestore";
import { firestore } from "../firestoreConfig";

// Interfaces for NDMO data structure
export interface Domain {
  id: string;
  name: string;
  totalControls: number;
  totalSpecs: number;
}

export interface Control {
  id: string;
  name: string;
  domainId: string;
  totalSpecs: number;
}

export interface Specification {
  id: string;
  name: string;
  priority: number;
  controlId: string;
  domainId: string;
}

// Main collection reference - this is the root collection
const ndmoCollectionRef = collection(firestore, "NDMO");

/**
 * Saves all NDMO data from Excel import
 * Creates hierarchical structure in Firestore:
 * - NDMO collection
 *   - Documents for each domain 
 *     - controls subcollection for each domain's controls
 *       - specifications subcollection for each control's specifications
 */
export const saveNDMOData = async (
  domains: Domain[],
  controls: Control[],
  specifications: Specification[]
): Promise<void> => {
  try {
    // Log what we're trying to save for debugging
    console.log("Saving to Firebase:", { 
      domains: domains.length, 
      controls: controls.length, 
      specs: specifications.length 
    });

    // Save domains first
    console.log(`Saving ${domains.length} domains...`);
    let currentBatch = writeBatch(firestore);
    let operationCount = 0;
    
    // Create metadata document to track imports
    const metadataRef = doc(ndmoCollectionRef, "metadata");
    currentBatch.set(metadataRef, {
      lastUpdated: serverTimestamp(),
      totalDomains: domains.length,
      totalControls: controls.length,
      totalSpecifications: specifications.length
    }, { merge: true });
    operationCount++;
    
    // Create a document for each domain directly in the NDMO collection
    for (const domain of domains) {
      const domainDocRef = doc(ndmoCollectionRef, domain.id);
      currentBatch.set(domainDocRef, {
        ...domain,
        timestamp: serverTimestamp() // Add timestamp for verification
      });
      operationCount++;
      
      // If batch is getting full, commit it and start a new one
      if (operationCount >= 400) {
        await currentBatch.commit();
        console.log(`Committed batch with ${operationCount} operations`);
        currentBatch = writeBatch(firestore);
        operationCount = 0;
      }
    }
    
    // Commit any remaining domain operations
    if (operationCount > 0) {
      await currentBatch.commit();
      console.log(`Committed batch with ${operationCount} domain operations`);
    }
    
    console.log("All domains saved successfully");

    // Now save controls for each domain
    console.log("Saving controls...");
    currentBatch = writeBatch(firestore);
    operationCount = 0;
    
    for (const domain of domains) {
      // Get domain-specific controls
      const domainControls = controls.filter(c => c.domainId === domain.id);
      
      // Create a controls subcollection for each domain
      for (const control of domainControls) {
        // Create a subcollection under the domain document
        const controlDocRef = doc(collection(ndmoCollectionRef, domain.id, "controls"), control.id);
        currentBatch.set(controlDocRef, {
          ...control,
          timestamp: serverTimestamp() // Add timestamp for verification
        });
        operationCount++;
        
        // If batch is getting full, commit it and start a new one
        if (operationCount >= 400) {
          await currentBatch.commit();
          console.log(`Committed batch with ${operationCount} operations`);
          currentBatch = writeBatch(firestore);
          operationCount = 0;
        }
      }
    }
    
    // Commit any remaining control operations
    if (operationCount > 0) {
      await currentBatch.commit();
      console.log(`Committed batch with ${operationCount} control operations`);
    }
    
    console.log("All controls saved successfully");

    // Finally save specifications for each control
    console.log("Saving specifications...");
    currentBatch = writeBatch(firestore);
    operationCount = 0;
    
    for (const domain of domains) {
      const domainControls = controls.filter(c => c.domainId === domain.id);
      
      for (const control of domainControls) {
        // Get control-specific specifications
        const controlSpecs = specifications.filter(s => s.controlId === control.id);
        
        // Create specifications subcollection for each control
        for (const spec of controlSpecs) {
          const specDocRef = doc(collection(ndmoCollectionRef, domain.id, "controls", control.id, "specifications"), spec.id);
          currentBatch.set(specDocRef, {
            ...spec,
            timestamp: serverTimestamp() // Add timestamp for verification
          });
          operationCount++;
          
          // If batch is getting full, commit it and start a new one
          if (operationCount >= 400) {
            await currentBatch.commit();
            console.log(`Committed batch with ${operationCount} operations`);
            currentBatch = writeBatch(firestore);
            operationCount = 0;
          }
        }
      }
    }
    
    // Commit any remaining specification operations
    if (operationCount > 0) {
      await currentBatch.commit();
      console.log(`Committed batch with ${operationCount} specification operations`);
    }
    
    console.log("NDMO data successfully saved to Firestore");
  } catch (error) {
    console.error("Error saving NDMO data:", error);
    throw error;
  }
};

/**
 * Retrieves all domains from Firestore
 */
export const getAllDomains = async (): Promise<Domain[]> => {
  try {
    // Domains are direct documents in the NDMO collection
    // Filter out the metadata document
    const querySnapshot = await getDocs(ndmoCollectionRef);
    console.log(`Raw domain query results: ${querySnapshot.size} documents found`);
    
    // Verify each document, exclude metadata document
    const domains = querySnapshot.docs
      .filter(doc => doc.id !== 'metadata') // Exclude the metadata document
      .map(doc => {
        const data = doc.data();
        console.log(`Domain document ${doc.id}:`, data);
        
        // Make sure it's a domain document
        if (data.name && data.totalControls !== undefined) {
          return {
            ...(data as Domain),
            id: doc.id
          };
        }
        return null;
      })
      .filter(domain => domain !== null) as Domain[];
    
    console.log(`Processed ${domains.length} domain documents`);
    return domains;
  } catch (error) {
    console.error("Error getting domains:", error);
    return []; // Return empty array instead of throwing
  }
};

/**
 * Retrieves controls for a specific domain
 */
export const getControlsByDomain = async (domainId: string): Promise<Control[]> => {
  try {
    const controlsCollectionRef = collection(ndmoCollectionRef, domainId, "controls");
    console.log(`Querying controls collection for domain ${domainId}:`, controlsCollectionRef.path);
    
    const querySnapshot = await getDocs(controlsCollectionRef);
    console.log(`Raw control query results for domain ${domainId}: ${querySnapshot.size} documents found`);
    
    const controls = querySnapshot.docs.map(doc => {
      const data = doc.data();
      console.log(`Control document ${doc.id} for domain ${domainId}:`, data);
      
      return {
        ...(data as Control),
        id: doc.id
      };
    });
    
    console.log(`Processed ${controls.length} control documents for domain ${domainId}`);
    return controls;
  } catch (error) {
    console.error(`Error getting controls for domain ${domainId}:`, error);
    return []; // Return empty array instead of throwing
  }
};

/**
 * Retrieves specifications for a specific control
 */
export const getSpecificationsByControl = async (domainId: string, controlId: string): Promise<Specification[]> => {
  try {
    // References to domain and control documents
    const specificationsCollectionRef = collection(ndmoCollectionRef, domainId, "controls", controlId, "specifications");
    console.log(`Querying specifications collection for control ${controlId} in domain ${domainId}:`, specificationsCollectionRef.path);
    
    const querySnapshot = await getDocs(specificationsCollectionRef);
    console.log(`Raw specification query results for control ${controlId}: ${querySnapshot.size} documents found`);
    
    const specifications = querySnapshot.docs.map(doc => {
      const data = doc.data();
      console.log(`Specification document ${doc.id} for control ${controlId}:`, data);
      
      return {
        ...(data as Specification),
        id: doc.id
      };
    });
    
    console.log(`Processed ${specifications.length} specification documents for control ${controlId}`);
    return specifications;
  } catch (error) {
    console.error(`Error getting specifications for control ${controlId}:`, error);
    return []; // Return empty array instead of throwing
  }
};

/**
 * Gets all NDMO data at once (domains, controls, and specifications)
 * Used to load all data when initializing the page
 */
export const getAllNDMOData = async (): Promise<{
  domains: Domain[];
  controls: Control[];
  specifications: Specification[];
}> => {
  try {
    console.log("Getting all NDMO data from Firebase...");
    
    // First check if we have a metadata document
    const metadataRef = doc(ndmoCollectionRef, "metadata");
    const metadataSnap = await getDoc(metadataRef);
    
    if (metadataSnap.exists()) {
      console.log("NDMO metadata found:", metadataSnap.data());
    } else {
      console.log("No NDMO metadata found. This might be a new installation.");
    }
    
    // Get all domains
    console.log("Fetching domains...");
    const domains = await getAllDomains();
    console.log(`Found ${domains.length} domains`);
    
    let allControls: Control[] = [];
    let allSpecifications: Specification[] = [];
    
    // For each domain, get its controls
    for (const domain of domains) {
      console.log(`Fetching controls for domain ${domain.id} (${domain.name})...`);
      const controls = await getControlsByDomain(domain.id);
      console.log(`Found ${controls.length} controls for domain ${domain.id}`);
      allControls = [...allControls, ...controls];
      
      // For each control, get its specifications
      for (const control of controls) {
        console.log(`Fetching specifications for control ${control.id} (${control.name})...`);
        const specs = await getSpecificationsByControl(domain.id, control.id);
        console.log(`Found ${specs.length} specifications for control ${control.id}`);
        allSpecifications = [...allSpecifications, ...specs];
      }
    }
    
    console.log("All NDMO data retrieved successfully");
    console.log(`Total: ${domains.length} domains, ${allControls.length} controls, ${allSpecifications.length} specifications`);
    
    return {
      domains,
      controls: allControls,
      specifications: allSpecifications
    };
  } catch (error) {
    console.error("Error getting all NDMO data:", error);
    
    // Return empty arrays instead of throwing to handle gracefully
    return {
      domains: [],
      controls: [],
      specifications: []
    };
  }
};

/**
 * Deletes all NDMO data
 * Used when reimporting data or clearing the database
 */
export const clearNDMOData = async (): Promise<void> => {
  try {
    console.log("Starting to clear NDMO data...");
    const domains = await getAllDomains();
    
    // Similar to saving, we'll handle deletions in multiple batches if needed
    const MAX_OPERATIONS = 400;
    
    // First delete all specifications
    console.log("Deleting specifications...");
    let currentBatch = writeBatch(firestore);
    let operationCount = 0;
    
    for (const domain of domains) {
      const domainControls = await getControlsByDomain(domain.id);
      
      for (const control of domainControls) {
        const specs = await getSpecificationsByControl(domain.id, control.id);
        
        for (const spec of specs) {
          const specRef = doc(collection(ndmoCollectionRef, domain.id, "controls", control.id, "specifications"), spec.id);
          currentBatch.delete(specRef);
          operationCount++;
          
          if (operationCount >= MAX_OPERATIONS) {
            await currentBatch.commit();
            console.log(`Committed deletion batch with ${operationCount} specification operations`);
            currentBatch = writeBatch(firestore);
            operationCount = 0;
          }
        }
      }
    }
    
    if (operationCount > 0) {
      await currentBatch.commit();
      console.log(`Committed deletion batch with ${operationCount} specification operations`);
    }
    
    // Now delete all controls
    console.log("Deleting controls...");
    currentBatch = writeBatch(firestore);
    operationCount = 0;
    
    for (const domain of domains) {
      const domainControls = await getControlsByDomain(domain.id);
      
      for (const control of domainControls) {
        const controlRef = doc(collection(ndmoCollectionRef, domain.id, "controls"), control.id);
        currentBatch.delete(controlRef);
        operationCount++;
        
        if (operationCount >= MAX_OPERATIONS) {
          await currentBatch.commit();
          console.log(`Committed deletion batch with ${operationCount} control operations`);
          currentBatch = writeBatch(firestore);
          operationCount = 0;
        }
      }
    }
    
    if (operationCount > 0) {
      await currentBatch.commit();
      console.log(`Committed deletion batch with ${operationCount} control operations`);
    }
    
    // Finally delete all domains
    console.log("Deleting domains...");
    currentBatch = writeBatch(firestore);
    operationCount = 0;
    
    for (const domain of domains) {
      const domainRef = doc(ndmoCollectionRef, domain.id);
      currentBatch.delete(domainRef);
      operationCount++;
      
      if (operationCount >= MAX_OPERATIONS) {
        await currentBatch.commit();
        console.log(`Committed deletion batch with ${operationCount} domain operations`);
        currentBatch = writeBatch(firestore);
        operationCount = 0;
      }
    }
    
    if (operationCount > 0) {
      await currentBatch.commit();
      console.log(`Committed deletion batch with ${operationCount} domain operations`);
    }
    
    // Update the metadata
    await setDoc(doc(ndmoCollectionRef, "metadata"), {
      lastUpdated: new Date(),
      totalDomains: 0,
      totalControls: 0,
      totalSpecifications: 0,
      cleared: true
    });
    
    console.log("All NDMO data successfully cleared");
  } catch (error) {
    console.error("Error clearing NDMO data:", error);
    throw error;
  }
}; 