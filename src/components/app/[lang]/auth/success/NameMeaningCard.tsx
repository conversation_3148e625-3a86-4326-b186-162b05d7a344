"use client";

import React, { useState, useCallback } from "react";
import { useChat } from "ai/react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Dictionary } from "@/dictionaries";
import { useToast } from "@/components/ui/use-toast";

interface NameMeaningCardProps {
  displayName: string | null | undefined;
  dict: Dictionary; // Pass the dictionary for localized text
}

export function NameMeaningCard({ displayName, dict }: NameMeaningCardProps) {
  const { toast } = useToast();
  const [showMeaning, setShowMeaning] = useState(false);

  const { messages, append, isLoading, error } = useChat({
    api: "/api/NameMeaning", // Target our API route
    initialMessages: [], // Start with no messages
    body: { name: displayName || "" }, // Send the name in the body
    onResponse: (response) => {
      if (!response.ok) {
         let errorMessage = dict.ai.nameMeaning.errorGeneric;
         if (response.status === 429) {
            errorMessage = dict.ai.nameMeaning.errorRateLimit;
         } else if (response.status >= 500) {
            errorMessage = dict.ai.nameMeaning.errorServer;
         }
         toast({
          title: dict.ai.nameMeaning.errorTitle,
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    onError: (err) => {
      console.error("Name Meaning API Error:", err);
      toast({
        title: dict.ai.nameMeaning.errorTitle,
        description: dict.ai.nameMeaning.errorNetwork || err.message,
        variant: "destructive",
      });
    },
    // We don't need automatic message sending on input change
  });

  const handleFindMeaning = useCallback(() => {
    if (!displayName) return; // Should not happen if button is disabled
    setShowMeaning(true);
    // Clear previous messages if any before appending
    messages.length = 0;
    append({
      role: "user",
      content: `What is the meaning of the name "${displayName}"?`, // This content isn't strictly needed by the API but good for useChat state
    });
  }, [displayName, append, messages]);

  // Get the latest assistant message content
  const assistantMessage = messages.find(m => m.role === 'assistant');

  if (!displayName) {
    // Don't render the card if there's no display name
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Sparkles className="mr-2 h-5 w-5 text-yellow-500" />
          {dict.ai.nameMeaning.title}
        </CardTitle>
        <CardDescription>
          {dict.ai.nameMeaning.description.replace("{{name}}", displayName)}
        </CardDescription>
      </CardHeader>
      {showMeaning && (
        <CardContent className="min-h-[50px]">
          {isLoading && assistantMessage?.content === '' && (
            <div className="flex items-center space-x-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>{dict.ai.nameMeaning.loading}...</span>
            </div>
          )}
          {assistantMessage?.content && (
            <p className="text-sm text-foreground whitespace-pre-wrap">
              {assistantMessage.content}
            </p>
          )}
          {error && !isLoading && (
            <p className="text-sm text-destructive">
               {dict.ai.nameMeaning.errorPrefix}: {error.message}
            </p>
          )}
        </CardContent>
      )}
      <CardFooter>
        <Button
          onClick={handleFindMeaning}
          disabled={isLoading || !displayName}
          size="sm"
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Sparkles className="mr-2 h-4 w-4" />
          )}
          {isLoading ? dict.ai.nameMeaning.buttonLoading : dict.ai.nameMeaning.buttonDefault}
        </Button>
      </CardFooter>
    </Card>
  );
} 