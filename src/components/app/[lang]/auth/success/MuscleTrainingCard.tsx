"use client";

import React, { useState } from "react";
import { useChat } from "ai/react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON>ader<PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Dictionary } from "@/dictionaries";
import { useToast } from "@/components/ui/use-toast";
import { Input } from "@/components/ui/input";

interface MuscleTrainingCardProps {
  dict: Dictionary; // Pass the dictionary for localized text
}

export function MuscleTrainingCard({ dict }: MuscleTrainingCardProps) {
  const { toast } = useToast();
  const [muscleName, setMuscleName] = useState<string>("");
  const [showWorkout, setShowWorkout] = useState(false);

  const { messages, append, isLoading, error } = useChat({
    api: "/api/MuscleTraining", // Target our API route
    initialMessages: [], // Start with no messages
    body: { muscle: muscleName || "" }, // Send the muscle name in the body
    onResponse: (response) => {
      if (!response.ok) {
         let errorMessage = dict.ai.muscleTraining?.errorGeneric || "An error occurred";
         if (response.status === 429) {
            errorMessage = dict.ai.muscleTraining?.errorRateLimit || "Rate limit exceeded";
         } else if (response.status >= 500) {
            errorMessage = dict.ai.muscleTraining?.errorServer || "Server error";
         }
         toast({
          title: dict.ai.muscleTraining?.errorTitle || "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    onError: (err) => {
      console.error("Muscle Training API Error:", err);
      toast({
        title: dict.ai.muscleTraining?.errorTitle || "Error",
        description: dict.ai.muscleTraining?.errorNetwork || err.message,
        variant: "destructive",
      });
    },
  });

  const handleGenerateWorkout = () => {
    if (!muscleName.trim()) {
      toast({
        title: dict.ai.muscleTraining?.inputRequired || "Required",
        description: dict.ai.muscleTraining?.pleaseEnterMuscle || "Please enter a muscle name",
        variant: "destructive",
      });
      return;
    }
    
    setShowWorkout(true);
    // Clear previous messages if any before appending
    messages.length = 0;
    append({
      role: "user",
      content: `Generate workout routine for ${muscleName}`,
    });
  };

  // Get the latest assistant message content
  const assistantMessage = messages.find(m => m.role === 'assistant');

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Dumbbell className="mr-2 h-5 w-5 text-blue-500" />
          {dict.ai.muscleTraining?.title || "Workout Generator"}
        </CardTitle>
        <CardDescription>
          {dict.ai.muscleTraining?.description || "Enter a muscle group to get a training routine"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex space-x-2 mb-4">
          <Input
            type="text"
            placeholder={dict.ai.muscleTraining?.inputPlaceholder || "e.g., biceps, chest, legs..."}
            value={muscleName}
            onChange={(e) => setMuscleName(e.target.value)}
            className="flex-1"
            disabled={isLoading}
          />
          <Button 
            onClick={handleGenerateWorkout} 
            disabled={isLoading || !muscleName.trim()}
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Dumbbell className="mr-2 h-4 w-4" />
            )}
            {dict.ai.muscleTraining?.generateButton || "Generate"}
          </Button>
        </div>

        {showWorkout && (
          <div className="min-h-[50px]">
            {isLoading && assistantMessage?.content === '' && (
              <div className="flex items-center space-x-2 text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>{dict.ai.muscleTraining?.loading || "Generating workout"}...</span>
              </div>
            )}
            {assistantMessage?.content && (
              <div className="text-sm text-foreground whitespace-pre-wrap border-l-2 border-blue-500 pl-3">
                {assistantMessage.content}
              </div>
            )}
            {error && !isLoading && (
              <p className="text-sm text-destructive">
                {dict.ai.muscleTraining?.errorPrefix || "Error"}: {error.message}
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 