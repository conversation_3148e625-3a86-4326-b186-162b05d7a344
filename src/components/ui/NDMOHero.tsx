"use client";

import React, { useEffect, useState } from "react";
import { Locale } from "@/i18n-config";

interface NDMOHeroProps {
  userName: string | null;
  lang: Locale;
}

export function NDMOHero({ userName, lang }: NDMOHeroProps) {
  const isArabic = lang === 'ar';
  const [animationState, setAnimationState] = useState(0);
  const [file, setFile] = useState<File | null>(null);
  
  // Animation effect for background gradient
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationState((prev) => (prev + 1) % 4);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  // Handle file upload
  const handleUpload = () => {
    if (!file) {
      alert(isArabic ? 'الرجاء اختيار ملف أولاً' : 'Please select a file first');
      return;
    }
    
    // Here we would typically handle the file upload to a server
    // For now, we'll just show an alert
    alert(isArabic 
      ? `تم استلام الملف: ${file.name}. سيتم معالجته قريباً.` 
      : `File received: ${file.name}. It will be processed soon.`);
  };

  return (
    <div className={`relative w-full h-screen flex flex-col items-center justify-start overflow-hidden ${isArabic ? 'rtl' : 'ltr'}`}>
      {/* Animated background gradient */}
      <div 
        className="absolute inset-0 -z-10 transition-all duration-1500 ease-in-out"
        style={{
          background: [
            'linear-gradient(135deg, var(--brand-dark-gray) 0%, var(--brand-blue) 100%)',
            'linear-gradient(225deg, var(--brand-blue) 0%, var(--brand-dark-gray) 100%)',
            'linear-gradient(315deg, var(--brand-dark-gray) 0%, var(--brand-blue) 100%)',
            'linear-gradient(45deg, var(--brand-blue) 0%, var(--brand-dark-gray) 100%)'
          ][animationState]
        }}
      ></div>
      
      {/* Large centered Thiqah logo */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="w-[80%] max-w-[600px] h-[80%] max-h-[600px] opacity-25 animate-float-slow">
          <img src="/image.png" alt="Thiqah Logo" className="w-full h-full object-contain" />
        </div>
      </div>
      
      {/* Animated decorative elements */}
      <div className="absolute top-10 right-10 -z-10 opacity-20 animate-pulse">
        <svg width="404" height="404" fill="none" viewBox="0 0 404 404">
          <defs>
            <pattern id="ndmo-pattern-1" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <rect x="0" y="0" width="4" height="4" className="text-white" fill="currentColor" />
            </pattern>
          </defs>
          <rect width="404" height="404" fill="url(#ndmo-pattern-1)" />
        </svg>
      </div>
      <div className="absolute bottom-10 left-10 -z-10 opacity-20 animate-pulse" style={{ animationDelay: '1.5s' }}>
        <svg width="404" height="404" fill="none" viewBox="0 0 404 404">
          <defs>
            <pattern id="ndmo-pattern-2" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <rect x="0" y="0" width="4" height="4" className="text-white" fill="currentColor" />
            </pattern>
          </defs>
          <rect width="404" height="404" fill="url(#ndmo-pattern-2)" />
        </svg>
      </div>

      {/* NDMO Hero content */}
      <div className="relative z-10 text-center max-w-5xl mx-auto px-6 animate-fadeIn mt-20">
        {/* Floating decorative elements */}
        <div className="absolute bottom-[-100px] right-[-50px] w-32 h-32 rounded-full bg-white/10 animate-float" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute top-[-80px] left-[-40px] w-24 h-24 rounded-full bg-white/10 animate-float" style={{ animationDelay: '1.2s' }}></div>
        <div className="absolute bottom-[20%] left-[-60px] w-20 h-20 rounded-full bg-white/10 animate-float" style={{ animationDelay: '0.8s' }}></div>
        
        {/* Main content */}
        <div className="mb-12">
          <div className="inline-flex items-center justify-center p-4 bg-white/20 backdrop-blur-md rounded-full mb-8 shadow-xl transform hover:scale-105 transition-all duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-14 w-14 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
          </div>
          <h1 className="text-6xl md:text-7xl font-bold text-white mb-6 tracking-tight">
            {isArabic ? `مركز البيانات الوطني، ${userName || 'المستخدم'}!` : `National Data Management Office, ${userName || 'User'}!`}
          </h1>
          <p className="text-2xl text-white/90 max-w-3xl mx-auto mb-12">
            {isArabic 
              ? 'مركز إدارة البيانات الوطنية لتحسين كفاءة وفعالية الخدمات الحكومية'
              : 'National Data Management Office for improving the efficiency and effectiveness of government services'}
          </p>
        </div>

        {/* Import NDMO Section */}
        <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 mb-16 max-w-2xl mx-auto animate-slideIn">
          <h2 className="text-3xl font-bold text-white mb-6">
            {isArabic ? 'استيراد بيانات NDMO' : 'Import NDMO Data'}
          </h2>
          <p className="text-lg text-white/90 mb-8">
            {isArabic 
              ? 'قم بتحميل ملف البيانات الخاص بك لتحليله ومعالجته'
              : 'Upload your data file for analysis and processing'}
          </p>
          
          <div className="flex flex-col items-center space-y-6">
            <div className="w-full">
              <label 
                htmlFor="ndmo-file-upload" 
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-white/30 border-dashed rounded-lg cursor-pointer bg-white/5 hover:bg-white/10 transition-all duration-300"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <svg className="w-10 h-10 mb-3 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <p className="mb-2 text-sm text-white/80">
                    <span className="font-semibold">{isArabic ? 'انقر للتحميل' : 'Click to upload'}</span> {isArabic ? 'أو اسحب وأفلت' : 'or drag and drop'}
                  </p>
                  <p className="text-xs text-white/60">{isArabic ? 'XLSX, CSV, أو JSON' : 'XLSX, CSV, or JSON'}</p>
                </div>
                <input 
                  id="ndmo-file-upload" 
                  type="file" 
                  className="hidden" 
                  accept=".xlsx,.csv,.json" 
                  onChange={handleFileChange}
                />
              </label>
            </div>
            
            {file && (
              <div className="bg-white/20 px-4 py-2 rounded-lg text-white w-full text-left">
                <p className="truncate">
                  <span className="font-medium">{isArabic ? 'الملف المختار:' : 'Selected file:'}</span> {file.name}
                </p>
              </div>
            )}
            
            <button 
              onClick={handleUpload}
              disabled={!file}
              className={`px-8 py-3 rounded-full text-lg font-bold shadow-lg transform transition-all duration-300 ${
                file 
                  ? 'bg-[var(--brand-blue)] text-white hover:translate-y-[-3px] hover:shadow-xl' 
                  : 'bg-gray-400 text-white/70 cursor-not-allowed'
              }`}
            >
              {isArabic ? 'استيراد البيانات' : 'Import Data'}
            </button>
          </div>
        </div>

        {/* Additional information or features can be added below */}
        <div className="relative">
          <button className="px-10 py-4 bg-white text-[var(--brand-blue)] text-xl font-bold rounded-full hover:bg-opacity-90 transition-all duration-300 shadow-xl transform hover:translate-y-[-3px] hover:scale-105 group">
            {isArabic ? 'معرفة المزيد' : 'Learn More'}
            <span className="absolute inset-0 rounded-full bg-white/30 group-hover:scale-110 transition-transform duration-500 blur-md -z-10"></span>
          </button>
        </div>
      </div>
    </div>
  );
}
