"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Search, FileSpreadsheet, Trash2, Plus, Brain, Loader2, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemData, SystemsService } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import * as XLSX from 'xlsx';

interface SystemDataTableProps {
  data: SystemData[];
  isLoading: boolean;
  isRTL: boolean;
  systemId: string;
  onImportClick: () => void;
  onDeleteAll?: () => void;
  onDataUpdate?: () => void;
  systemContext?: string;
}

export function SystemDataTable({ data, isLoading, isRTL, systemId, onImportClick, onDeleteAll, onDataUpdate, systemContext }: SystemDataTableProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isClassifying, setIsClassifying] = useState(false);
  const [isClassifyingPage, setIsClassifyingPage] = useState(false);
  const [classificationProgress, setClassificationProgress] = useState<{
    stage: 'idle' | 'table_classification' | 'page_classification' | 'completed';
    currentPage?: number;
    totalPages?: number;
  }>({ stage: 'idle' });
  const rowsPerPage = 5; // Changed to 5 records per page to avoid token limits

  // Filter data based on search term
  const filteredData = data.filter(row => 
    row.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    row.columnName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    row.dataType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (row.schemaName && row.schemaName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination
  const totalPages = Math.ceil(filteredData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Table Classification function (one-time setup)
  const handleTableClassification = async () => {
    if (data.length === 0) {
      toast({
        title: isRTL ? "لا توجد بيانات" : "No Data",
        description: isRTL ? "لا توجد بيانات للتصنيف" : "No data available for classification",
        variant: "destructive"
      });
      return;
    }

    setIsClassifying(true);
    setClassificationProgress({ stage: 'table_classification' });

    try {
      toast({
        title: isRTL ? "تصنيف الجداول" : "Classifying Tables",
        description: isRTL ? "تصنيف أنواع الجداول..." : "Classifying table types...",
      });

      const tableClassificationResponse = await fetch('/api/ai/classification/table-classification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          systemData: data,
          systemId
        })
      });

      if (!tableClassificationResponse.ok) {
        const errorData = await tableClassificationResponse.json();
        throw new Error(errorData.error || 'Table classification failed');
      }

      const tableClassificationResult = await tableClassificationResponse.json();

      // Update data with table classifications
      const tableUpdates: Array<{
        documentId: string;
        data: Partial<Pick<SystemData, 'tableType' | 'dataCategory' | 'classificationStatus'>>;
      }> = [];

      data.forEach(record => {
        if (record.id) {
          const tableClassification = tableClassificationResult.classifications.find(
            (c: any) => c.tableName === record.tableName &&
                      (c.schemaName || 'default') === (record.schemaName || 'default')
          );

          if (tableClassification) {
            tableUpdates.push({
              documentId: record.id,
              data: {
                tableType: tableClassification.tableType,
                dataCategory: tableClassification.dataCategory,
                classificationStatus: 'table_classified'
              }
            });
          }
        }
      });

      // Batch update table classifications
      if (tableUpdates.length > 0) {
        try {
          await SystemsService.updateSystemDataBatch(systemId, tableUpdates);
        } catch (updateError) {
          console.warn('Some documents could not be updated:', updateError);
          // Continue with success message even if some updates failed
          // The batch update function already handles missing documents gracefully
        }
      }

      toast({
        title: isRTL ? "تم تصنيف الجداول" : "Tables Classified",
        description: isRTL ?
          `تم تصنيف ${tableUpdates.length} جدول` :
          `Classified ${tableUpdates.length} tables`,
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }

    } catch (error) {
      console.error('Table classification error:', error);
      toast({
        title: isRTL ? "خطأ في تصنيف الجداول" : "Table Classification Error",
        description: error instanceof Error ? error.message : "An error occurred during table classification",
        variant: "destructive"
      });
    } finally {
      setIsClassifying(false);
      setClassificationProgress({ stage: 'idle' });
    }
  };

  // Page Classification function (manual, current page only)
  const handlePageClassification = async () => {
    if (currentData.length === 0) {
      toast({
        title: isRTL ? "لا توجد بيانات" : "No Data",
        description: isRTL ? "لا توجد بيانات في هذه الصفحة" : "No data on this page",
        variant: "destructive"
      });
      return;
    }

    setIsClassifyingPage(true);

    try {
      toast({
        title: isRTL ? "تصنيف الصفحة" : "Classifying Page",
        description: isRTL ?
          `تصنيف ${currentData.length} سجل في الصفحة ${currentPage}` :
          `Classifying ${currentData.length} records on page ${currentPage}`,
      });

      const pageClassificationResponse = await fetch('/api/ai/classification/page-classification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pageData: currentData,
          systemId,
          pageNumber: currentPage,
          startIndex: startIndex + 1,
          endIndex: Math.min(endIndex, filteredData.length),
          systemContext: systemContext || undefined
        })
      });

      if (!pageClassificationResponse.ok) {
        const errorData = await pageClassificationResponse.json();
        throw new Error(errorData.error || 'Page classification failed');
      }

      const pageClassificationResult = await pageClassificationResponse.json();

      // Update page classifications
      const pageUpdates: Array<{
        documentId: string;
        data: Partial<Pick<SystemData, 'confidentialityLevel' | 'confidentialityReasoning' | 'hasPersonalData' | 'personalDataReasoning' | 'classificationStatus'>>;
      }> = [];

      pageClassificationResult.classifications.forEach((classification: any) => {
        pageUpdates.push({
          documentId: classification.recordId,
          data: {
            confidentialityLevel: classification.confidentialityLevel,
            confidentialityReasoning: classification.confidentialityReasoning,
            hasPersonalData: classification.hasPersonalData,
            personalDataReasoning: classification.personalDataReasoning,
            classificationStatus: 'fully_classified'
          }
        });
      });

      // Batch update page classifications
      if (pageUpdates.length > 0) {
        try {
          await SystemsService.updateSystemDataBatch(systemId, pageUpdates);
        } catch (updateError) {
          console.warn('Some documents could not be updated:', updateError);
          // Continue with success message even if some updates failed
          // The batch update function already handles missing documents gracefully
        }
      }

      toast({
        title: isRTL ? "تم تصنيف الصفحة" : "Page Classified",
        description: isRTL ?
          `تم تصنيف ${pageUpdates.length} سجل` :
          `Classified ${pageUpdates.length} records`,
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }

    } catch (error) {
      console.error('Page classification error:', error);
      toast({
        title: isRTL ? "خطأ في تصنيف الصفحة" : "Page Classification Error",
        description: error instanceof Error ? error.message : "An error occurred during page classification",
        variant: "destructive"
      });
    } finally {
      setIsClassifyingPage(false);
    }
  };

  // Excel Export function
  const handleExcelExport = () => {
    try {
      // Prepare column headers based on language
      const headers = isRTL ? {
        'رقم الصف': 'Row #',
        'اسم المخطط': 'Schema Name',
        'اسم الجدول': 'Table Name',
        'اسم العمود': 'Column Name',
        'نوع البيانات': 'Data Type',
        'الحد الأقصى': 'Max Length',
        'يقبل القيم الفارغة': 'Nullable',
        'ترتيب العمود': 'Column Order',
        'آخر بحث': 'Last Seek',
        'آخر فحص': 'Last Scan',
        'آخر استعلام': 'Last Lookup',
        'آخر تحديث': 'Last Update',
        'نوع الجدول': 'Table Type',
        'فئة البيانات': 'Data Category',
        'مستوى السرية': 'Confidentiality Level',
        'سبب السرية': 'Confidentiality Reasoning',
        'بيانات شخصية': 'Has Personal Data',
        'سبب البيانات الشخصية': 'Personal Data Reasoning',
        'حالة التصنيف': 'Classification Status',
        'ترتيب الاستيراد': 'Import Order',
        'تاريخ الإنشاء': 'Created At'
      } : {
        'Row #': 'Row #',
        'Schema Name': 'Schema Name',
        'Table Name': 'Table Name',
        'Column Name': 'Column Name',
        'Data Type': 'Data Type',
        'Max Length': 'Max Length',
        'Nullable': 'Nullable',
        'Column Order': 'Column Order',
        'Last Seek': 'Last Seek',
        'Last Scan': 'Last Scan',
        'Last Lookup': 'Last Lookup',
        'Last Update': 'Last Update',
        'Table Type': 'Table Type',
        'Data Category': 'Data Category',
        'Confidentiality Level': 'Confidentiality Level',
        'Confidentiality Reasoning': 'Confidentiality Reasoning',
        'Has Personal Data': 'Has Personal Data',
        'Personal Data Reasoning': 'Personal Data Reasoning',
        'Classification Status': 'Classification Status',
        'Import Order': 'Import Order',
        'Created At': 'Created At'
      };

      // Prepare data for export with proper formatting
      const exportData = filteredData.map((row, index) => {
        const rowData: Record<string, any> = {};
        const headerKeys = Object.keys(headers);

        rowData[headerKeys[0]] = index + 1; // Row #
        rowData[headerKeys[1]] = row.schemaName || ''; // Schema Name
        rowData[headerKeys[2]] = row.tableName || ''; // Table Name
        rowData[headerKeys[3]] = row.columnName || ''; // Column Name
        rowData[headerKeys[4]] = row.dataType || ''; // Data Type
        rowData[headerKeys[5]] = row.maxLength || ''; // Max Length
        rowData[headerKeys[6]] = row.isNullable !== null ? (row.isNullable ? (isRTL ? 'نعم' : 'Yes') : (isRTL ? 'لا' : 'No')) : ''; // Nullable
        rowData[headerKeys[7]] = row.columnOrder || ''; // Column Order
        rowData[headerKeys[8]] = row.lastSeek || ''; // Last Seek
        rowData[headerKeys[9]] = row.lastScan || ''; // Last Scan
        rowData[headerKeys[10]] = row.lastLookup || ''; // Last Lookup
        rowData[headerKeys[11]] = row.lastUpdate || ''; // Last Update
        rowData[headerKeys[12]] = row.tableType ? (row.tableType === 'system_table' ? (isRTL ? 'جدول نظام' : 'System Table') : (isRTL ? 'جدول بيانات' : 'Data Table')) : ''; // Table Type
        rowData[headerKeys[13]] = row.dataCategory ? (row.dataCategory === 'customers' ? (isRTL ? 'عملاء' : 'Customers') : (isRTL ? 'فريق التطوير' : 'Development Team')) : ''; // Data Category
        rowData[headerKeys[14]] = row.confidentialityLevel ? (isRTL ? (
          row.confidentialityLevel === 'Public' ? 'عام' :
          row.confidentialityLevel === 'Confidential' ? 'سري' :
          row.confidentialityLevel === 'Secret' ? 'سري جداً' : 'سري للغاية'
        ) : row.confidentialityLevel) : ''; // Confidentiality Level
        rowData[headerKeys[15]] = row.confidentialityReasoning || ''; // Confidentiality Reasoning
        rowData[headerKeys[16]] = row.hasPersonalData !== undefined ? (row.hasPersonalData ? (isRTL ? 'نعم' : 'Yes') : (isRTL ? 'لا' : 'No')) : ''; // Has Personal Data
        rowData[headerKeys[17]] = row.personalDataReasoning || ''; // Personal Data Reasoning
        rowData[headerKeys[18]] = row.classificationStatus ? (isRTL ? (
          row.classificationStatus === 'pending' ? 'في الانتظار' :
          row.classificationStatus === 'table_classified' ? 'مصنف جزئياً' : 'مصنف بالكامل'
        ) : row.classificationStatus) : ''; // Classification Status
        rowData[headerKeys[19]] = row.importOrder || ''; // Import Order
        rowData[headerKeys[20]] = row.createdAt ? new Date(row.createdAt.toMillis()).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : ''; // Created At

        return rowData;
      });

      // Create workbook and worksheet
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();

      // Set column widths for better formatting
      const colWidths = [
        { wch: 8 },   // Row #
        { wch: 15 },  // Schema Name
        { wch: 20 },  // Table Name
        { wch: 25 },  // Column Name
        { wch: 15 },  // Data Type
        { wch: 12 },  // Max Length
        { wch: 10 },  // Nullable
        { wch: 12 },  // Column Order
        { wch: 15 },  // Last Seek
        { wch: 15 },  // Last Scan
        { wch: 15 },  // Last Lookup
        { wch: 15 },  // Last Update
        { wch: 15 },  // Table Type
        { wch: 18 },  // Data Category
        { wch: 18 },  // Confidentiality Level
        { wch: 30 },  // Confidentiality Reasoning
        { wch: 18 },  // Has Personal Data
        { wch: 30 },  // Personal Data Reasoning
        { wch: 18 },  // Classification Status
        { wch: 12 },  // Import Order
        { wch: 15 }   // Created At
      ];
      ws['!cols'] = colWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, isRTL ? 'بيانات النظام' : 'System Data');

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = isRTL ? `تصدير_بيانات_النظام_${timestamp}.xlsx` : `system_data_export_${timestamp}.xlsx`;

      // Save file
      XLSX.writeFile(wb, filename);

      toast({
        title: isRTL ? "تم تصدير البيانات" : "Data Exported",
        description: isRTL ?
          `تم تصدير ${exportData.length} سجل إلى ملف Excel` :
          `Exported ${exportData.length} records to Excel file`,
      });

    } catch (error) {
      console.error('Excel export error:', error);
      toast({
        title: isRTL ? "خطأ في التصدير" : "Export Error",
        description: error instanceof Error ? error.message : "An error occurred during export",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div className="p-8 text-center">
          <div className="animate-pulse space-y-4">
            <div className="w-32 h-32 bg-gray-200 rounded-3xl mx-auto"></div>
            <div className="h-6 bg-gray-200 rounded w-48 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-32 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
      >
        <div className="p-12 text-center">
          <div className="w-32 h-32 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-8">
            <FileSpreadsheet className="w-16 h-16 text-[var(--brand-blue)]" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {isRTL ? "لا توجد بيانات" : "No Data Available"}
          </h3>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            {isRTL 
              ? "لم يتم استيراد أي بيانات بعد. قم بتحميل ملف Excel لبدء إدارة بيانات النظام."
              : "No data has been imported yet. Upload an Excel file to start managing system data."
            }
          </p>
          <Button
            onClick={onImportClick}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            {isRTL ? "استيراد البيانات" : "Import Data"}
          </Button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <FileSpreadsheet className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">
                {isRTL ? "بيانات النظام" : "System Data"}
              </h3>
              <p className="text-white/80">
                {isRTL ? `${data.length} سجل` : `${data.length} records`}
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={handleTableClassification}
              disabled={isClassifying || data.length === 0}
              className="bg-purple-500/20 backdrop-blur-sm border border-purple-300/50 text-white hover:bg-purple-500/30 disabled:opacity-50"
            >
              {isClassifying ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Brain className="w-4 h-4 mr-2" />
              )}
              {isClassifying
                ? (isRTL ? "جاري تصنيف الجداول..." : "Classifying Tables...")
                : (isRTL ? "تصنيف الجداول" : "Classify Tables")
              }
            </Button>
            <Button
              onClick={handlePageClassification}
              disabled={isClassifyingPage || currentData.length === 0}
              className="bg-blue-500/20 backdrop-blur-sm border border-blue-300/50 text-white hover:bg-blue-500/30 disabled:opacity-50"
            >
              {isClassifyingPage ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Brain className="w-4 h-4 mr-2" />
              )}
              {isClassifyingPage
                ? (isRTL ? "جاري تصنيف الصفحة..." : "Classifying Page...")
                : (isRTL ? `تصنيف الصفحة ${currentPage}` : `Classify Page ${currentPage}`)
              }
            </Button>
            <Button
              onClick={handleExcelExport}
              disabled={data.length === 0}
              className="bg-green-500/20 backdrop-blur-sm border border-green-300/50 text-white hover:bg-green-500/30 disabled:opacity-50"
            >
              <Download className="w-4 h-4 mr-2" />
              {isRTL ? "تصدير Excel" : "Export Excel"}
            </Button>
            <Button
              onClick={onImportClick}
              className="bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30"
            >
              <Plus className="w-4 h-4 mr-2" />
              {isRTL ? "استيراد" : "Import"}
            </Button>
            {onDeleteAll && data.length > 0 && (
              <Button
                onClick={onDeleteAll}
                variant="outline"
                className="bg-red-500/20 border-red-300/50 text-white hover:bg-red-500/30"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {isRTL ? "حذف الكل" : "Delete All"}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={isRTL ? "البحث في البيانات..." : "Search data..."}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
            />
          </div>
          <div className="text-sm text-gray-600">
            {isRTL
              ? `عرض ${startIndex + 1}-${Math.min(endIndex, filteredData.length)} من ${filteredData.length}`
              : `Showing ${startIndex + 1}-${Math.min(endIndex, filteredData.length)} of ${filteredData.length}`
            }
          </div>
        </div>

        {/* Classification Progress */}
        {(classificationProgress.stage !== 'idle' || isClassifyingPage) && (
          <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <div className="flex items-center gap-3">
              <Loader2 className="w-5 h-5 text-purple-600 animate-spin" />
              <div className="flex-1">
                <div className="text-sm font-medium text-purple-900">
                  {classificationProgress.stage === 'table_classification' && (
                    isRTL ? "تصنيف الجداول..." : "Classifying tables..."
                  )}
                  {isClassifyingPage && (
                    isRTL
                      ? `تصنيف الصفحة ${currentPage} (${currentData.length} سجل)`
                      : `Classifying page ${currentPage} (${currentData.length} records)`
                  )}
                  {classificationProgress.stage === 'completed' && (
                    isRTL ? "تم التصنيف بنجاح" : "Classification completed"
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "اسم المخطط" : "Schema Name"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "اسم الجدول" : "Table Name"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "اسم العمود" : "Column Name"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "نوع البيانات" : "Data Type"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "الحد الأقصى" : "Max Length"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "يقبل القيم الفارغة" : "Nullable"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "نوع الجدول" : "Table Type"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "فئة البيانات" : "Data Category"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "مستوى السرية" : "Confidentiality"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "سبب السرية" : "Confidentiality Reason"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "بيانات شخصية" : "Personal Data"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "سبب البيانات الشخصية" : "Personal Data Reason"}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {currentData.map((row, index) => (
              <motion.tr
                key={`${row.id}-${index}`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="hover:bg-gray-50 transition-colors"
              >
                <td className="px-6 py-4 text-sm text-gray-600">
                  {row.schemaName || '-'}
                </td>
                <td className="px-6 py-4 text-sm font-medium text-gray-900">
                  {row.tableName}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {row.columnName}
                </td>
                <td className="px-6 py-4 text-sm">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]">
                    {row.dataType}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-600">
                  {row.maxLength || '-'}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.isNullable !== null ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.isNullable
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {row.isNullable ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.tableType ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.tableType === 'system_table'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {row.tableType === 'system_table'
                        ? (isRTL ? "جدول نظام" : "System")
                        : (isRTL ? "جدول بيانات" : "Data")
                      }
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.dataCategory ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.dataCategory === 'customers'
                        ? 'bg-purple-100 text-purple-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {row.dataCategory === 'customers'
                        ? (isRTL ? "عملاء" : "Customers")
                        : (isRTL ? "فريق التطوير" : "Dev Team")
                      }
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.confidentialityLevel ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.confidentialityLevel === 'Public' ? 'bg-green-100 text-green-800' :
                      row.confidentialityLevel === 'Confidential' ? 'bg-yellow-100 text-yellow-800' :
                      row.confidentialityLevel === 'Secret' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {isRTL ? (
                        row.confidentialityLevel === 'Public' ? 'عام' :
                        row.confidentialityLevel === 'Confidential' ? 'سري' :
                        row.confidentialityLevel === 'Secret' ? 'سري جداً' : 'سري للغاية'
                      ) : row.confidentialityLevel}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.confidentialityReasoning ? (
                    <div
                      className="text-xs text-gray-700 max-w-xs"
                      title={row.confidentialityReasoning.length > 50 ? row.confidentialityReasoning : undefined}
                    >
                      {row.confidentialityReasoning.length > 50
                        ? `${row.confidentialityReasoning.substring(0, 50)}...`
                        : row.confidentialityReasoning
                      }
                    </div>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.hasPersonalData !== undefined ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.hasPersonalData
                        ? 'bg-red-100 text-red-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {row.hasPersonalData ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.personalDataReasoning ? (
                    <div
                      className="text-xs text-gray-700 max-w-xs"
                      title={row.personalDataReasoning.length > 50 ? row.personalDataReasoning : undefined}
                    >
                      {row.personalDataReasoning.length > 50
                        ? `${row.personalDataReasoning.substring(0, 50)}...`
                        : row.personalDataReasoning
                      }
                    </div>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {isRTL 
                ? `صفحة ${currentPage} من ${totalPages}`
                : `Page ${currentPage} of ${totalPages}`
              }
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
              >
                {isRTL ? "السابق" : "Previous"}
              </Button>
              <Button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                variant="outline"
                size="sm"
              >
                {isRTL ? "التالي" : "Next"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
} 