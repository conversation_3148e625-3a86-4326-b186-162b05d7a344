"use client";

import React from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";

interface SpecificationData {
  id: string;
  name: string;
  priority: number;
  controlId: string;
  domainId: string;
}

interface SpecificationsTableProps {
  specifications: SpecificationData[];
  lang: Locale;
}

export function SpecificationsTable({ specifications, lang }: SpecificationsTableProps) {
  const isRTL = lang === "ar";
  const direction = isRTL ? "rtl" : "ltr";
  
  const renderPriorityBadge = (priority: number) => {
    let bgColor = "bg-green-500";
    let textColor = "text-green-50";
    let label = isRTL ? `الأولوية ${priority}` : `Priority ${priority}`;
    
    if (priority === 2) {
      bgColor = "bg-yellow-500";
      textColor = "text-yellow-50";
    } else if (priority === 3) {
      bgColor = "bg-red-500";
      textColor = "text-red-50";
    }
    
    return (
      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
        {label}
      </div>
    );
  };
  
  if (specifications.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 text-center"
        style={{ direction }}
      >
        <svg className="w-12 h-12 text-white/30 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>
        <p className="text-white/60">
          {isRTL ? "لا توجد مواصفات للعرض. الرجاء تحديد ضابط من القائمة." : "No specifications to display. Please select a control from the list."}
        </p>
      </motion.div>
    );
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden"
      style={{ direction }}
    >
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-white/10">
          <thead className="bg-white/5">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                {isRTL ? "رمز المواصفة" : "Spec ID"}
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                {isRTL ? "اسم المواصفة" : "Specification Name"}
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                {isRTL ? "الأولوية" : "Priority"}
              </th>
            </tr>
          </thead>
          <tbody className="bg-white/5 divide-y divide-white/10">
            {specifications.map((spec, index) => (
              <motion.tr 
                key={spec.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="hover:bg-white/10 transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                  <span className="font-mono">{spec.id}</span>
                </td>
                <td className="px-6 py-4 text-sm text-white">
                  {spec.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {renderPriorityBadge(spec.priority)}
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </motion.div>
  );
} 