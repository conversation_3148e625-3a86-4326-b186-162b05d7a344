"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import * as XLSX from "xlsx";
import { Locale } from "@/i18n-config";

interface ExcelUploaderProps {
  onUploadSuccess: (data: any[], stats: { domains: number, controls: number, specs: number }) => void;
  lang: Locale;
}

export function ExcelUploader({ onUploadSuccess, lang }: ExcelUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const isRTL = lang === "ar";
  
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };
  
  const processExcelData = async (file: File) => {
    setIsProcessing(true);
    setError(null);
    
    try {
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      
      // Validate the data has the expected format
      if (jsonData.length === 0) {
        throw new Error(isRTL ? "ملف فارغ. يرجى تحميل ملف يحتوي على بيانات." : "Empty file. Please upload a file with data.");
      }
      
      const requiredColumns = ["Domain ID", "Domain", "Dimension", "Control ID", "Control Name", "Specs #", "Specification Name", "Priority"];
      const firstRow = jsonData[0] as any;
      
      for (const column of requiredColumns) {
        if (!(column in firstRow)) {
          throw new Error(
            isRTL
              ? `العمود المطلوب "${column}" غير موجود في الملف.`
              : `Required column "${column}" is missing from the file.`
          );
        }
      }
      
      // Count unique domains, controls, and specifications
      const domains = new Set(jsonData.map((row: any) => row["Domain ID"]));
      const controls = new Set(jsonData.map((row: any) => row["Control ID"]));
      const specs = jsonData.length;
      
      const stats = {
        domains: domains.size,
        controls: controls.size,
        specs: specs
      };
      
      onUploadSuccess(jsonData as any[], stats);
    } catch (err: any) {
      console.error("Error processing Excel file:", err);
      setError(err.message || "An error occurred while processing the file");
    } finally {
      setIsProcessing(false);
    }
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      
      // Check if it's an Excel file
      if (
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel" ||
        file.name.endsWith(".xlsx") ||
        file.name.endsWith(".xls")
      ) {
        processExcelData(file);
      } else {
        setError(isRTL ? "يرجى تحميل ملف Excel (.xlsx أو .xls)" : "Please upload an Excel file (.xlsx or .xls)");
      }
    }
  };
  
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      processExcelData(file);
    }
  };
  
  return (
    <div className="w-full max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full"
      >
        <div
          className={`
            w-full h-64 border-2 border-dashed rounded-xl flex flex-col items-center justify-center p-6
            ${isDragging ? "border-[var(--brand-blue)] bg-[var(--brand-blue)]/10" : "border-white/20 bg-white/5"}
            backdrop-blur-md transition-all duration-300 relative
          `}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {isProcessing ? (
            <div className="text-center">
              <div className="w-16 h-16 border-t-2 border-b-2 border-[var(--brand-blue)] rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-white/80">{isRTL ? "جاري معالجة الملف..." : "Processing file..."}</p>
            </div>
          ) : (
            <>
              <svg 
                className="w-16 h-16 text-white/40 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 className="text-xl font-semibold text-white mb-2">
                {isRTL ? "قم بسحب وإفلات ملف Excel هنا" : "Drag & Drop Excel File Here"}
              </h3>
              <p className="text-white/60 text-center mb-4">
                {isRTL 
                  ? "أو انقر لاختيار الملف. يجب أن يحتوي على جميع الأعمدة المطلوبة."
                  : "Or click to browse. File must contain all required columns."}
              </p>
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileInput}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <div className="flex flex-wrap justify-center gap-2 mt-2">
                {["Domain ID", "Domain", "Control ID", "Control Name", "Specs #", "Specification Name", "Priority"].map((column) => (
                  <span 
                    key={column}
                    className="text-xs px-2 py-1 bg-white/10 rounded-md text-white/70"
                  >
                    {column}
                  </span>
                ))}
              </div>
            </>
          )}
        </div>
        
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-white"
          >
            <p className="text-sm">{error}</p>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
} 