"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Locale } from '@/i18n-config';

interface PodcastResponse {
  success: boolean;
  url: string;
  fileName: string;
  language: string;
  error?: string;
}

type ScriptLanguage = 'en' | 'ar' | 'ar-eg';

/**
 * Component for generating and playing NDMO data as a podcast
 * 
 * @param lang - The current language (en or ar)
 */
export const NDMOPodcast: React.FC<{ lang: Locale }> = ({ lang }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingScript, setIsGeneratingScript] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [scriptText, setScriptText] = useState<string | null>(null);
  const [scriptLanguage, setScriptLanguage] = useState<ScriptLanguage>(lang === 'ar' ? 'ar' : 'en');
  const scriptRef = useRef<HTMLDivElement>(null);
  const isRTL = lang === 'ar';

  /**
   * Handles script language selection
   */
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setScriptLanguage(e.target.value as ScriptLanguage);
  };

  /**
   * Generates a script from NDMO data and automatically generates podcast after completion
   */
  const generateScript = async () => {
    try {
      setIsGeneratingScript(true);
      setError(null);
      setScriptText(null);
      
      // Call the script API with the selected language
      const response = await fetch('/api/ndmo-script', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          language: scriptLanguage
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate script');
      }
      
      // Stream the response text
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let result = '';
      
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          result += decoder.decode(value, { stream: true });
          setScriptText(result);
        }
        
        // Make sure to get the last chunk
        result += decoder.decode();
        setScriptText(result);
        
        // Automatically generate podcast with the script
        await generatePodcast(result);
      }
      
      // Scroll to the script container
      setTimeout(() => {
        if (scriptRef.current) {
          scriptRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
      
    } catch (error) {
      console.error('Error generating script:', error);
      setError((error as Error).message || 'An unexpected error occurred');
    } finally {
      setIsGeneratingScript(false);
    }
  };
  
  /**
   * Generates a podcast from NDMO data and optionally uses a provided script
   */
  const generatePodcast = async (scriptToUse?: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Call the updated API endpoint with the current language and optional script
      const response = await fetch('/api/ndmo-podcast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lang,
          script: scriptToUse
        }),
      });
      
      const data: PodcastResponse = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.success ? 'Unknown error' : data.error || 'Failed to generate podcast');
      }
      
      // Set the audio URL to the generated podcast
      setAudioUrl(data.url);
    } catch (error) {
      console.error('Error generating podcast:', error);
      setError((error as Error).message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-[var(--brand-blue)]/20">
      <div className="flex items-center mb-4">
        <div className="w-10 h-10 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center mr-3">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[var(--brand-blue)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-[var(--brand-dark-gray)]">
          {isRTL ? 'استمع إلى البيانات كبودكاست' : 'Listen to Data as Podcast'}
        </h3>
      </div>
      
      <p className="text-sm text-[var(--brand-dark-gray)]/70 mb-4">
        {isRTL 
          ? 'استمع إلى بيانات NDMO التي تم تحويلها إلى بودكاست صوتي باستخدام تقنية الذكاء الاصطناعي.'
          : 'Listen to NDMO data converted to an audio podcast using AI technology.'}
      </p>

      {/* Script Generation Section */}
      <div className="mb-6 border-b border-gray-200 pb-6">
        <div className="flex items-center mb-3">
          <div className="w-8 h-8 rounded-full bg-[var(--brand-blue)]/10 flex items-center justify-center mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[var(--brand-blue)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <h4 className="text-md font-medium text-[var(--brand-dark-gray)]">
            {isRTL ? 'توليد النص أولاً' : 'Generate Script First'}
          </h4>
        </div>

        <div className="flex flex-col space-y-3">
          {/* Language Selector */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex-grow">
              <label htmlFor="script-language" className="block text-sm text-[var(--brand-dark-gray)]/70 mb-1">
                {isRTL ? 'اختر لغة النص:' : 'Choose script language:'}
              </label>
              <select
                id="script-language"
                value={scriptLanguage}
                onChange={handleLanguageChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--brand-blue)] text-[var(--brand-dark-gray)]"
                disabled={isGeneratingScript}
              >
                <option value="en">English</option>
                <option value="ar">العربية الفصحى</option>
                <option value="ar-eg">العربية المصرية</option>
              </select>
            </div>

            {/* Generate Script Button */}
            <div className="flex items-end">
              <button
                onClick={generateScript}
                disabled={isGeneratingScript}
                className={`flex items-center justify-center px-4 py-2 rounded-lg w-full sm:w-auto ${
                  isGeneratingScript
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-[var(--brand-blue)]/80 hover:bg-[var(--brand-blue)]/70'
                } text-white transition-colors duration-300`}
              >
                {isGeneratingScript ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isRTL ? 'جارٍ التوليد...' : 'Generating...'}
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {isRTL ? 'توليد النص' : 'Generate Script'}
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Generated Script Display */}
          {scriptText && (
            <div 
              ref={scriptRef}
              className="mt-3 p-4 border border-[var(--brand-blue)]/20 rounded-lg bg-[var(--brand-blue)]/5 max-h-[350px] overflow-y-auto whitespace-pre-wrap"
            >
              <h5 className="font-medium text-[var(--brand-dark-gray)] mb-2">
                {isRTL ? 'النص المولّد:' : 'Generated Script:'}
              </h5>
              <div className="text-sm text-[var(--brand-dark-gray)]/90 font-mono">
                {scriptText}
              </div>
              <div className="mt-2 text-right">
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(scriptText);
                    alert(isRTL ? 'تم نسخ النص إلى الحافظة' : 'Script copied to clipboard');
                  }}
                  className="text-xs text-[var(--brand-blue)] hover:underline"
                >
                  {isRTL ? 'نسخ النص' : 'Copy Script'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Podcast Generation Section */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center mb-2">
          <div className="w-8 h-8 rounded-full bg-[var(--brand-blue)]/10 flex items-center justify-center mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[var(--brand-blue)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 017.072 0m-9.9-2.828a9 9 0 0112.728 0" />
            </svg>
          </div>
          <h4 className="text-md font-medium text-[var(--brand-dark-gray)]">
            {isRTL ? 'البودكاست يتم توليده تلقائيًا' : 'Podcast Generated Automatically'}
          </h4>
        </div>
        
        <p className="text-xs text-[var(--brand-dark-gray)]/70 mb-2">
          {isRTL 
            ? 'بمجرد توليد النص، سيتم تلقائيًا إنشاء بودكاست صوتي.'
            : 'Once the script is generated, an audio podcast will be created automatically.'}
        </p>
        
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            {error}
          </div>
        )}
        
        {audioUrl && (
          <div className="p-4 border border-[var(--brand-blue)]/20 rounded-lg bg-[var(--brand-blue)]/5">
            <p className="text-sm text-[var(--brand-dark-gray)] mb-2">
              {isRTL ? 'البودكاست جاهز للاستماع:' : 'Your podcast is ready to listen:'}
            </p>
            <audio 
              controls 
              className="w-full" 
              src={audioUrl || undefined}
            >
              {isRTL 
                ? 'متصفحك لا يدعم عنصر الصوت.' 
                : 'Your browser does not support the audio element.'}
            </audio>
            <div className="mt-2 text-right">
              <a 
                href={audioUrl || '#'} 
                download={!!audioUrl}
                className="text-xs text-[var(--brand-blue)] hover:underline"
              >
                {isRTL ? 'تحميل البودكاست' : 'Download Podcast'}
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
