"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { X, Save, FileText, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

interface ContextModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (context: string) => Promise<void>;
  isRTL: boolean;
  currentContext?: string;
}

export function ContextModal({ isOpen, onClose, onSave, isRTL, currentContext = "" }: ContextModalProps) {
  const [context, setContext] = useState(currentContext);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const handleSave = async () => {
    if (!context.trim()) {
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: isRTL ? "يرجى إدخال نص السياق" : "Please enter context text",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);
      await onSave(context.trim());
      toast({
        title: isRTL ? "تم الحفظ بنجاح" : "Saved Successfully",
        description: isRTL ? "تم حفظ السياق بنجاح" : "Context saved successfully",
      });
      onClose();
    } catch (error) {
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: error instanceof Error ? error.message : "Failed to save context",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    setContext(currentContext);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={`max-w-4xl max-h-[90vh] overflow-hidden ${isRTL ? "rtl" : "ltr"}`}>
        <DialogHeader className="border-b border-gray-100 pb-6">
          <DialogTitle className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-[var(--brand-blue)] to-purple-600 rounded-xl flex items-center justify-center">
              <FileText className="w-5 h-5 text-white" />
            </div>
            {isRTL ? "سياق النظام" : "System Context"}
          </DialogTitle>
          <DialogDescription className="text-gray-600 mt-2">
            {isRTL 
              ? "أضف أي معلومات إضافية أو سياق مهم حول هذا النظام"
              : "Add any additional information or important context about this system"
            }
          </DialogDescription>
        </DialogHeader>

        <div className="p-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="relative">
              <label className="block text-sm font-semibold text-gray-700 mb-3">
                <div className="flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "نص السياق" : "Context Text"}
                </div>
              </label>
              <textarea
                value={context}
                onChange={(e) => setContext(e.target.value)}
                placeholder={isRTL 
                  ? "اكتب هنا أي معلومات إضافية حول النظام، مثل الغرض منه، المتطلبات الخاصة، التحديات، أو أي تفاصيل مهمة أخرى..."
                  : "Write here any additional information about the system, such as its purpose, special requirements, challenges, or any other important details..."
                }
                className="w-full h-80 p-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none resize-none transition-all duration-200 text-gray-900 placeholder-gray-500"
                style={{ minHeight: '320px' }}
              />
              <div className="absolute bottom-3 right-3 text-xs text-gray-400">
                {context.length} {isRTL ? "حرف" : "characters"}
              </div>
            </div>

            {/* Context Tips */}
            <div className="bg-gradient-to-r from-[var(--brand-blue)]/5 to-purple-50 rounded-xl p-4 border border-[var(--brand-blue)]/10">
              <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                <Sparkles className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "اقتراحات للسياق" : "Context Suggestions"}
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• {isRTL ? "الغرض الأساسي من النظام" : "Primary purpose of the system"}</li>
                <li>• {isRTL ? "المستخدمون المستهدفون" : "Target users"}</li>
                <li>• {isRTL ? "المتطلبات الخاصة أو القيود" : "Special requirements or constraints"}</li>
                <li>• {isRTL ? "التحديات المعروفة" : "Known challenges"}</li>
                <li>• {isRTL ? "خطط التطوير المستقبلية" : "Future development plans"}</li>
              </ul>
            </div>
          </motion.div>
        </div>

        {/* Footer Actions */}
        <div className="border-t border-gray-100 pt-6 px-6 pb-6 flex justify-between">
          <Button onClick={handleClose} variant="outline">
            {isRTL ? "إلغاء" : "Cancel"}
          </Button>
          
          <Button 
            onClick={handleSave}
            disabled={isSaving || !context.trim()}
            className="bg-gradient-to-r from-[var(--brand-blue)] to-purple-600 hover:from-[var(--brand-blue)]/90 hover:to-purple-600/90 text-white"
          >
            {isSaving ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {isRTL ? "جاري الحفظ..." : "Saving..."}
              </div>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                {isRTL ? "حفظ السياق" : "Save Context"}
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 