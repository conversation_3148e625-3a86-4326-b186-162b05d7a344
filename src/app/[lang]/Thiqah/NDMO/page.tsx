"use client";

import React, { useState, useEffect, useRef } from "react";
import { useParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { Locale } from "@/i18n-config";
import { ExcelUploader } from "@/components/ui/ExcelUploader";
import { ImportStats } from "@/components/ui/ImportStats";
import { DomainCard } from "@/components/ui/DomainCard";
import { ControlCard } from "@/components/ui/ControlCard";
import { SpecificationsTable } from "@/components/ui/SpecificationsTable";
import Image from "next/image";
import { 
  saveNDMOData, 
  getAllNDMOData, 
  getAllDomains,
  getControlsByDomain,
  getSpecificationsByControl,
  clearNDMOData 
} from "@/Firebase/firestore/services/NDMOService";
import { NDMOPodcast } from "@/components/ui/NDMOPodcast";

interface ExcelDataRow {
  "Domain ID": string;
  "Domain": string;
  "Dimension": string;
  "Control ID": string;
  "Control Name": string;
  "Specs #": string;
  "Specification Name": string;
  "Priority": number;
}

interface Domain {
  id: string;
  name: string;
  totalControls: number;
  totalSpecs: number;
}

interface Control {
  id: string;
  name: string;
  domainId: string;
  totalSpecs: number;
}

interface Specification {
  id: string;
  name: string;
  priority: number;
  controlId: string;
  domainId: string;
}

export default function NDMOPage() {
  const params = useParams();
  const lang = (params?.lang as Locale) || "en";
  const isRTL = lang === "ar";
  const direction = isRTL ? "rtl" : "ltr";
  
  const [fileName, setFileName] = useState<string>("");
  const [excelData, setExcelData] = useState<ExcelDataRow[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [importStats, setImportStats] = useState<{domains: number, controls: number, specs: number} | null>(null);
  const [domains, setDomains] = useState<Domain[]>([]);
  const [controls, setControls] = useState<Control[]>([]);
  const [specifications, setSpecifications] = useState<Specification[]>([]);
  
  const [activeDomainId, setActiveDomainId] = useState<string | null>(null);
  const [activeControlId, setActiveControlId] = useState<string | null>(null);
  
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  // State for preview modal
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [processedData, setProcessedData] = useState<{
    domains: Domain[];
    controls: Control[];
    specifications: Specification[];
  } | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  // To track navigation level (0: Domains, 1: Controls, 2: Specifications)
  const [navLevel, setNavLevel] = useState<number>(0);
  
  // Reference to file input
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Import statistics 'type safe' check helper
  const getStatsSafe = () => {
    return importStats || { 
      domains: domains.length, 
      controls: controls.length, 
      specs: specifications.length 
    };
  };
  
  // Load only domains from Firebase on component mount
  useEffect(() => {
    const loadDomainsData = async () => {
      try {
        setIsLoading(true);
        console.log("Loading domains from Firebase...");
        
        // Only fetch domains initially
        const domainsData = await getAllDomains();
        
        // If we have domains in Firebase, set them to state
        if (domainsData.length > 0) {
          console.log("Domains found in Firebase:", domainsData);
          setDomains(domainsData);
          
          // Set domains count in stats but only if there are domains
          setImportStats({
            domains: domainsData.length,
            controls: 0,
            specs: 0
          });
        } else {
          console.log("No domains found in Firebase");
          // Make sure importStats is null if no domains found
          setImportStats(null);
        }
      } catch (error) {
        console.error("Error loading domains:", error);
        setIsLoading(false);
        // Make sure importStats is null if error occurred
        setImportStats(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadDomainsData();
  }, []);
  
  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setFileName(file.name);
      processExcelFile(file);
    }
  };
  
  // Process the Excel file
  const processExcelFile = async (file: File) => {
    try {
      setIsLoading(true);
      
      const { read, utils } = await import('xlsx');
      const data = await file.arrayBuffer();
      const workbook = read(data);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = utils.sheet_to_json(worksheet);
      
      if (jsonData.length === 0) {
        throw new Error("Empty file");
      }
      
      // Validate required columns
      const requiredColumns = ["Domain ID", "Domain", "Dimension", "Control ID", "Control Name", "Specs #", "Specification Name", "Priority"];
      const firstRow = jsonData[0] as any;
      
      for (const column of requiredColumns) {
        if (!(column in firstRow)) {
          throw new Error(`Required column "${column}" is missing from the file.`);
        }
      }
      
      // Count unique domains, controls, and specifications
      const domains = new Set(jsonData.map((row: any) => row["Domain ID"]));
      const controls = new Set(jsonData.map((row: any) => row["Control ID"]));
      const specs = jsonData.length;
      
      const stats = {
        domains: domains.size,
        controls: controls.size,
        specs: specs
      };
      
      // If there's existing data, clear it first
      if (importStats !== null) {
        try {
          console.log("Clearing existing data before import...");
          await clearNDMOData();
          console.log("Existing data cleared successfully");
        } catch (error) {
          console.error("Error clearing existing data:", error);
        }
      }
      
      // Process the data but don't save it yet, just preview it
      const processed = processExcelData(jsonData as ExcelDataRow[]);
      setProcessedData(processed);
      
      // Show preview modal
      setShowPreviewModal(true);
    } catch (error) {
      console.error("Error processing Excel file:", error);
      alert("Error processing Excel file. Please ensure it contains all required columns.");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle actual saving to Firebase after confirmation
  const handleSaveToFirebase = async () => {
    if (!processedData) return;
    
    setIsSaving(true);
    try {
      // Show loading indicator or message
      console.log("Saving data to Firebase...");
      
      // Save the processed data to Firebase
      await saveNDMOData(
        processedData.domains, 
        processedData.controls, 
        processedData.specifications
      );
      
      // Update the local state with the processed data
      setDomains(processedData.domains);
      setControls(processedData.controls);
      setSpecifications(processedData.specifications);
      
      console.log("Data successfully saved to Firebase");
      setShowPreviewModal(false);
    } catch (error) {
      console.error("Error saving data to Firebase:", error);
      alert("There was an error saving data to Firebase. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };
  
  // Process Excel data to extract domains, controls, and specifications
  const processExcelData = (data: ExcelDataRow[]) => {
    // Extract unique domains
    const uniqueDomains = new Map<string, Domain>();
    const uniqueControls = new Map<string, Control>();
    const allSpecifications: Specification[] = [];
    
    // First pass: Count controls and specs per domain and per control
    const domainControlsCount = new Map<string, Set<string>>();
    const domainSpecsCount = new Map<string, number>();
    const controlSpecsCount = new Map<string, number>();
    
    data.forEach((row) => {
      const domainId = row["Domain ID"].toString();
      const controlId = row["Control ID"].toString();
      
      // Count unique controls per domain
      if (!domainControlsCount.has(domainId)) {
        domainControlsCount.set(domainId, new Set());
      }
      domainControlsCount.get(domainId)?.add(controlId);
      
      // Count specs per domain
      domainSpecsCount.set(domainId, (domainSpecsCount.get(domainId) || 0) + 1);
      
      // Count specs per control
      controlSpecsCount.set(controlId, (controlSpecsCount.get(controlId) || 0) + 1);
    });
    
    // Second pass: Create structured data
    data.forEach((row) => {
      const domainId = row["Domain ID"].toString();
      const domainName = row["Domain"].toString();
      const controlId = row["Control ID"].toString();
      const controlName = row["Control Name"].toString();
      const specId = row["Specs #"].toString();
      const specName = row["Specification Name"].toString();
      const priority = Number(row["Priority"]);
      
      // Add domain if not exists
      if (!uniqueDomains.has(domainId)) {
        uniqueDomains.set(domainId, {
          id: domainId,
          name: domainName,
          totalControls: domainControlsCount.get(domainId)?.size || 0,
          totalSpecs: domainSpecsCount.get(domainId) || 0
        });
      }
      
      // Add control if not exists
      if (!uniqueControls.has(controlId)) {
        uniqueControls.set(controlId, {
          id: controlId,
          name: controlName,
          domainId: domainId,
          totalSpecs: controlSpecsCount.get(controlId) || 0
        });
      }
      
      // Add specification
      allSpecifications.push({
        id: specId,
        name: specName,
        priority: priority,
        controlId: controlId,
        domainId: domainId
      });
    });
    
    const processedDomains = Array.from(uniqueDomains.values());
    const processedControls = Array.from(uniqueControls.values());
    
    // Update state with processed data
    setDomains(processedDomains);
    setControls(processedControls);
    setSpecifications(allSpecifications);
    
    // Reset navigation level to domains view
    setNavLevel(0);
    setActiveDomainId(null);
    setActiveControlId(null);
    
    // Return the processed data for Firebase saving
    return {
      domains: processedDomains,
      controls: processedControls,
      specifications: allSpecifications
    };
  };
  
  // Filter controls by active domain
  const filteredControls = activeDomainId
    ? controls.filter((control) => control.domainId === activeDomainId)
    : [];
    
  // Filter specifications by active control
  const filteredSpecifications = activeControlId
    ? specifications.filter((spec) => spec.controlId === activeControlId)
    : [];
  
  // Handle domain click to navigate to its controls
  const handleDomainClick = async (domainId: string) => {
    setIsLoading(true);
    try {
      // Load controls for this domain
      const domainControls = await getControlsByDomain(domainId);
      
      // Update controls state
      setControls(prevControls => {
        // Filter out controls for this domain (if any exist) and add new ones
        const filteredControls = prevControls.filter(control => control.domainId !== domainId);
        return [...filteredControls, ...domainControls];
      });
      
      // Update stats
      setImportStats(prev => prev ? {
        ...prev,
        controls: (prev.controls || 0) + domainControls.length
      } : {
        domains: domains.length,
        controls: domainControls.length,
        specs: 0
      });
      
      // Set active domain and navigate to controls level
      setActiveDomainId(domainId);
      setNavLevel(1);
    } catch (error) {
      console.error(`Error loading controls for domain ${domainId}:`, error);
      alert(`Error loading controls for this domain. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle control click to navigate to its specifications
  const handleControlClick = async (controlId: string) => {
    setIsLoading(true);
    try {
      if (!activeDomainId) {
        console.error("No active domain selected");
        return;
      }
      
      // Load specifications for this control
      const controlSpecs = await getSpecificationsByControl(activeDomainId, controlId);
      
      // Update specifications state
      setSpecifications(prevSpecs => {
        // Filter out specifications for this control (if any exist) and add new ones
        const filteredSpecs = prevSpecs.filter(spec => spec.controlId !== controlId);
        return [...filteredSpecs, ...controlSpecs];
      });
      
      // Update stats
      setImportStats(prev => prev ? {
        ...prev,
        specs: (prev.specs || 0) + controlSpecs.length
      } : {
        domains: domains.length,
        controls: controls.length,
        specs: controlSpecs.length
      });
      
      // Set active control and navigate to specifications level
      setActiveControlId(controlId);
      setNavLevel(2);
    } catch (error) {
      console.error(`Error loading specifications for control ${controlId}:`, error);
      alert(`Error loading specifications for this control. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle back navigation
  const handleBackClick = () => {
    if (navLevel === 2) {
      // Going back from specifications to controls
      setActiveControlId(null);
      setNavLevel(1);
    } else if (navLevel === 1) {
      // Going back from controls to domains
      setActiveDomainId(null);
      setNavLevel(0);
    }
  };
  
  // Get the active domain name
  const activeDomain = activeDomainId 
    ? domains.find(domain => domain.id === activeDomainId)
    : null;
  
  // Get the active control name
  const activeControl = activeControlId 
    ? controls.find(control => control.id === activeControlId)
    : null;
  
  // Go back to previous level
  const handleGoBack = () => {
    if (navLevel === 2) {
      setNavLevel(1);
      setActiveControlId(null);
    } else if (navLevel === 1) {
      setNavLevel(0);
      setActiveDomainId(null);
    }
  };
  
  return (
    <div 
      className="relative min-h-screen w-full overflow-hidden m-0 p-0"
      style={{ direction, margin: 0, padding: 0 }}
    >
      {/* Animated Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1a1a20] via-[var(--brand-dark-gray)] to-[#1a1a20] z-0">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_10%_20%,rgba(35,169,219,0.4)_0%,transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_80%_80%,rgba(35,169,219,0.4)_0%,transparent_50%)]"></div>
        </div>
      </div>
      
      {/* Animated Grid Pattern */}
      <div className="absolute inset-0 z-0 opacity-10">
        <div className="h-full w-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyM0E5REIiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRoLTJ2LTRoMnY0em0wLTZ2LTRoLTJ2NGgyek0yNCAzNGgtMnYtNGgydjR6bTAtNnYtNGgtMnY0aDJ6Ii8+PC9nPjwvZz48L3N2Zz4=')]"></div>
      </div>
      
      {/* Floating Thiqah Logo Watermark */}
      <div className="absolute inset-0 z-0 flex items-center justify-center opacity-5">
        <div className={`animate-float-slow ${isRTL ? "ml-0" : "mr-0"}`}>
          <Image 
            src="/thiqah-logo.png" 
            alt="Thiqah Logo" 
            width={600} 
            height={600}
            className="opacity-30"
          />
        </div>
      </div>
      
      {/* Futuristic Lines */}
      <div className="absolute inset-0 z-0">
        <motion.div 
          className="absolute top-0 left-0 h-[1px] bg-gradient-to-r from-transparent via-[var(--brand-blue)]/30 to-transparent"
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ duration: 2, delay: 0.5 }}
        />
        <motion.div 
          className="absolute bottom-0 right-0 h-[1px] bg-gradient-to-l from-transparent via-[var(--brand-blue)]/30 to-transparent"
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ duration: 2, delay: 0.7 }}
        />
        <motion.div 
          className="absolute top-0 right-0 w-[1px] bg-gradient-to-b from-transparent via-[var(--brand-blue)]/30 to-transparent"
          initial={{ height: 0 }}
          animate={{ height: "100%" }}
          transition={{ duration: 2, delay: 0.9 }}
        />
        <motion.div 
          className="absolute bottom-0 left-0 w-[1px] bg-gradient-to-t from-transparent via-[var(--brand-blue)]/30 to-transparent"
          initial={{ height: 0 }}
          animate={{ height: "100%" }}
          transition={{ duration: 2, delay: 1.1 }}
        />
      </div>
      
      {/* Main Content */}
      <div className="relative z-10 w-full min-h-screen flex flex-col items-center justify-center p-0 m-0">
        {/* Loading Indicator */}
        {isLoading && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="bg-[#2A2A35]/80 rounded-lg p-8 flex flex-col items-center">
              <div className="w-16 h-16 border-4 border-t-[var(--brand-blue)] border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-white text-lg">{isRTL ? "جاري التحميل..." : "Loading..."}</p>
            </div>
          </div>
        )}
      
        {/* Initial Hero - Only show when no data exists */}
        {domains.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="text-center w-full"
          >
            <motion.div 
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                {isRTL ? 'المركز الوطني لإدارة البيانات' : 'National Data Management Office'}
              </h1>
              <p className="text-2xl text-[var(--brand-blue)] mb-5">
                {isRTL ? 'منصة الرؤية الإستراتيجية والضوابط' : 'Strategic Foresight & Controls Platform'}
              </p>
              <p className="text-xl text-[var(--brand-blue)]/80 mb-4">
                {isRTL 
                  ? 'NDMO - معيار المركز الوطني لإدارة البيانات'
                  : 'NDMO - National Data Management Office Standard'}
              </p>
              <p className="text-white/70 text-lg max-w-3xl mx-auto mb-10">
                {isRTL 
                  ? 'تم إنشاء المركز الوطني لإدارة البيانات في المملكة العربية السعودية بهدف تحسين إدارة البيانات الوطنية وتطوير سياسات وأطر العمل والمعايير للبيانات. يساهم المركز في تعزيز حوكمة البيانات وتطوير القدرات الوطنية في مجال البيانات والذكاء الاصطناعي، تماشياً مع رؤية المملكة 2030.'
                  : 'The National Data Management Office in Saudi Arabia was established to improve national data governance and develop data policies, frameworks, and standards. The office contributes to enhancing data governance and developing national capabilities in data and artificial intelligence, in line with Saudi Vision 2030.'}
              </p>
              
              <div className="flex justify-center gap-6 mb-8">
                <div className="bg-[#2A2A35]/40 backdrop-blur-sm rounded-lg px-6 py-3 w-[180px]">
                  <span className="text-[var(--brand-blue)] text-3xl font-semibold block">14</span>
                  <span className="text-white/70 text-sm">
                    {isRTL ? 'المجالات' : 'Domains'}
                  </span>
                </div>
                <div className="bg-[#2A2A35]/40 backdrop-blur-sm rounded-lg px-6 py-3 w-[180px]">
                  <span className="text-purple-400 text-3xl font-semibold block">77</span>
                  <span className="text-white/70 text-sm">
                    {isRTL ? 'الضوابط' : 'Controls'}
                  </span>
                </div>
                <div className="bg-[#2A2A35]/40 backdrop-blur-sm rounded-lg px-6 py-3 w-[180px]">
                  <span className="text-green-400 text-3xl font-semibold block">191</span>
                  <span className="text-white/70 text-sm">
                    {isRTL ? 'المواصفات' : 'Specifications'}
                  </span>
                </div>
              </div>
              
              <button
                onClick={() => fileInputRef.current?.click()}
                className="mt-8 px-8 py-4 bg-[var(--brand-blue)] text-white rounded-full font-bold text-lg shadow-lg shadow-[var(--brand-blue)]/20 relative overflow-hidden group hover:scale-105 transition-transform duration-300"
              >
                <span className="relative z-10">
                  {isRTL ? 'تحميل ملف Excel' : 'Upload Excel File'}
                </span>
              </button>
              <input 
                type="file" 
                ref={fileInputRef}
                accept=".xlsx,.xls" 
                className="hidden"
                onChange={handleFileChange}
              />
              
              <p className="text-white/50 text-sm mt-4">
                {isRTL 
                  ? 'يجب أن يحتوي الملف على الأعمدة: Domain ID, Domain, Dimension, Control ID, Control Name, Specs #, Specification Name, Priority'
                  : 'File must contain columns: Domain ID, Domain, Dimension, Control ID, Control Name, Specs #, Specification Name, Priority'}
              </p>
            </motion.div>
            
            {/* Animated Features */}
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              {[
                {
                  title: isRTL ? "تنظيم المعلومات" : "Organize Information",
                  description: isRTL ? "ترتيب البيانات بشكل هرمي: المجالات، الضوابط، والمواصفات" : "Hierarchical data organization: domains, controls, and specifications",
                  icon: (
                    <svg className="w-6 h-6 text-[var(--brand-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  ),
                },
                {
                  title: isRTL ? "رؤية واضحة" : "Clear Visualization",
                  description: isRTL ? "عرض البيانات بطريقة تفاعلية وسهلة الفهم" : "Interactive and intuitive data visualization",
                  icon: (
                    <svg className="w-6 h-6 text-[var(--brand-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                    </svg>
                  ),
                },
                {
                  title: isRTL ? "تحليل متقدم" : "Advanced Analysis",
                  description: isRTL ? "تحليل العلاقات بين المجالات والضوابط والمواصفات" : "Analyze relationships between domains, controls, and specifications",
                  icon: (
                    <svg className="w-6 h-6 text-[var(--brand-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  ),
                },
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 + (index * 0.1), duration: 0.5 }}
                  className="bg-[#2A2A35]/40 backdrop-blur-sm border border-white/5 rounded-xl p-6 hover:bg-[#2A2A35]/60 transition-all duration-300"
                >
                  <div className="h-12 w-12 rounded-full bg-[var(--brand-blue)]/10 flex items-center justify-center mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
                  <p className="text-white/70">{feature.description}</p>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        )}
        
        {/* Main Data Content */}
        {domains.length > 0 && (
          <div className="w-full">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="px-6 py-12 w-full"
            >
                {/* Hero Stats Section */}
                <div className="mb-12 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-[var(--brand-blue)]/10 to-[var(--brand-blue)]/5 rounded-2xl -z-10 backdrop-blur-sm border border-[var(--brand-blue)]/20"></div>
                                  <div className="absolute inset-0 overflow-hidden rounded-2xl -z-20">
                    <div className="absolute -right-12 -top-12 w-64 h-64 bg-[var(--brand-blue)]/20 rounded-full filter blur-3xl"></div>
                    <div className="absolute -left-12 -bottom-12 w-64 h-64 bg-[var(--brand-blue)]/20 rounded-full filter blur-3xl"></div>
                </div>
                
                <div className="py-10 px-8">
                  <motion.h1 
                    className="text-3xl md:text-4xl font-bold text-[#3D3D45] mb-4 text-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.7 }}
                  >
                    <span className="text-[#23A9DB]">{isRTL ? "معيار" : "NDMO"}</span> {isRTL ? "المركز الوطني لإدارة البيانات" : "Standard"}
                  </motion.h1>
                  
                  <motion.div 
                    className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: 0.3 }}
                  >
                    <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-[var(--brand-blue)]/20 flex items-center">
                      <div className="w-14 h-14 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center mr-5">
                        <svg className="w-7 h-7 text-[var(--brand-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-4xl font-bold text-[var(--brand-dark-gray)]">{domains.length}</div>
                        <div className="text-[var(--brand-blue)] font-medium">{isRTL ? "المجالات" : "Domains"}</div>
                      </div>
                    </div>
                    
                    <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-[var(--brand-blue)]/20 flex items-center">
                      <div className="w-14 h-14 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center mr-5">
                        <svg className="w-7 h-7 text-[var(--brand-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-4xl font-bold text-[var(--brand-dark-gray)]">{controls.length}</div>
                        <div className="text-[var(--brand-blue)] font-medium">{isRTL ? "الضوابط" : "Controls"}</div>
                      </div>
                    </div>
                    
                    <div className="bg-white/60 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-[var(--brand-blue)]/20 flex items-center">
                      <div className="w-14 h-14 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center mr-5">
                        <svg className="w-7 h-7 text-[var(--brand-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-4xl font-bold text-[var(--brand-dark-gray)]">{specifications.length}</div>
                        <div className="text-[var(--brand-blue)] font-medium">{isRTL ? "المواصفات" : "Specifications"}</div>
                      </div>
                    </div>
                  </motion.div>
                  
                  <motion.div 
                    className="mt-8 text-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.7, delay: 0.5 }}
                  >
                                         <p className="text-[var(--brand-dark-gray)]/80 text-lg">
                        {isRTL 
                          ? "استكشف البيانات المنظمة للمركز الوطني لإدارة البيانات. اضغط على أي مجال لعرض الضوابط المرتبطة به."
                          : "Explore NDMO structured data. Click on any domain to view its associated controls."}
                     </p>
                  </motion.div>
                </div>
              </div>
              
              {/* Domain Statistics */}
              <ImportStats stats={getStatsSafe()} lang={lang} />
              
              {/* Podcast Feature */}
              <div className="mt-6">
                <NDMOPodcast lang={lang} />
              </div>
              
              {/* Navigation Breadcrumb */}
              {navLevel > 0 && (
                <motion.div 
                  className="flex items-center mt-8 mb-4"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <button 
                    onClick={handleGoBack}
                    className="flex items-center text-white/70 hover:text-white transition-colors bg-white/5 hover:bg-white/10 rounded-lg px-3 py-1.5"
                  >
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    {isRTL ? "عودة" : "Back"}
                  </button>
                  
                  <div className="flex items-center text-white/50 ml-3">
                    <button 
                      onClick={() => setNavLevel(0)}
                      className={`hover:text-white transition-colors ${navLevel === 0 ? 'text-[var(--brand-blue)]' : ''}`}
                    >
                      {isRTL ? "المجالات" : "Domains"}
                    </button>
                    
                    {navLevel >= 1 && activeDomain && (
                      <>
                        <span className="mx-2">/</span>
                        <button 
                          onClick={() => setNavLevel(1)}
                          className={`hover:text-white transition-colors ${navLevel === 1 ? 'text-[var(--brand-blue)]' : ''}`}
                        >
                          {activeDomain.name}
                        </button>
                      </>
                    )}
                    
                    {navLevel >= 2 && activeControl && (
                      <>
                        <span className="mx-2">/</span>
                        <span className={`${navLevel === 2 ? 'text-[var(--brand-blue)]' : ''}`}>
                          {activeControl.name}
                        </span>
                      </>
                    )}
                  </div>
                </motion.div>
              )}
              
              {/* Content Screens - Each level is shown exclusively */}
              <AnimatePresence mode="wait">
                {/* Domains View */}
                {navLevel === 0 && (
                  <motion.div
                    key="domains-view"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className="mt-6"
                  >
                    <h2 className="text-2xl font-bold text-white mb-6">
                      {isRTL ? "المجالات" : "Domains"}
                    </h2>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {domains.map((domain, index) => (
                        <DomainCard 
                          key={domain.id}
                          domain={domain}
                          onClick={() => handleDomainClick(domain.id)}
                          isActive={domain.id === activeDomainId}
                          index={index}
                          lang={lang}
                        />
                      ))}
                    </div>
                  </motion.div>
                )}
                
                {/* Controls View */}
                {navLevel === 1 && activeDomain && (
                  <motion.div
                    key="controls-view"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                    className="mt-6"
                  >
                    <div className="flex items-center mb-6">
                      <div className="h-12 w-12 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center">
                        <svg className="w-6 h-6 text-[var(--brand-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <h2 className="text-2xl font-bold text-white">
                          {activeDomain.name}
                        </h2>
                        <p className="text-[var(--brand-blue)]">
                          {isRTL ? `${activeDomain.totalControls} ضوابط و ${activeDomain.totalSpecs} مواصفات` : `${activeDomain.totalControls} controls and ${activeDomain.totalSpecs} specifications`}
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5">
                      {filteredControls.map((control, index) => (
                        <ControlCard 
                          key={control.id}
                          control={control}
                          onClick={() => handleControlClick(control.id)}
                          isActive={control.id === activeControlId}
                          index={index}
                          lang={lang}
                        />
                      ))}
                    </div>
                  </motion.div>
                )}
                
                {/* Specifications View */}
                {navLevel === 2 && activeControl && (
                  <motion.div
                    key="specs-view"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.3 }}
                    className="mt-6"
                  >
                    <div className="flex items-center mb-6">
                      <div className="h-12 w-12 rounded-full bg-purple-500/20 flex items-center justify-center">
                        <svg className="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <h2 className="text-2xl font-bold text-white">
                          {activeControl.name}
                        </h2>
                        <p className="text-purple-400">
                          {isRTL ? `${activeControl.totalSpecs} مواصفات` : `${activeControl.totalSpecs} specifications`}
                        </p>
                      </div>
                    </div>
                    
                    <SpecificationsTable 
                      specifications={filteredSpecifications}
                      lang={lang}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
        )}
        
        {/* Preview Modal */}
        {showPreviewModal && processedData && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
              <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 className="text-xl font-semibold text-gray-800">{isRTL ? 'مراجعة البيانات قبل الحفظ' : 'Review Data Before Saving'}</h3>
                <button 
                  onClick={() => setShowPreviewModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="overflow-auto p-4 flex-grow">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                    <h4 className="font-semibold text-blue-700 mb-2">{isRTL ? 'المجالات' : 'Domains'}</h4>
                    <p className="text-blue-600">{processedData.domains.length} {isRTL ? 'مجال تمت معالجته' : 'domains processed'}</p>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                    <h4 className="font-semibold text-green-700 mb-2">{isRTL ? 'الضوابط' : 'Controls'}</h4>
                    <p className="text-green-600">{processedData.controls.length} {isRTL ? 'ضابط تمت معالجته' : 'controls processed'}</p>
                  </div>
                  <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                    <h4 className="font-semibold text-purple-700 mb-2">{isRTL ? 'المواصفات' : 'Specifications'}</h4>
                    <p className="text-purple-600">{processedData.specifications.length} {isRTL ? 'مواصفة تمت معالجتها' : 'specifications processed'}</p>
                  </div>
                </div>
                
                <div className="space-y-6">
                  {/* Domains Preview */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-100 px-4 py-2 font-medium">{isRTL ? 'المجالات (تعرض أول 5)' : 'Domains (Showing first 5)'}</div>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'الاسم' : 'Name'}</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'الضوابط' : 'Controls'}</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'المواصفات' : 'Specs'}</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {processedData.domains.slice(0, 5).map((domain) => (
                            <tr key={domain.id}>
                              <td className="px-4 py-2 text-sm text-gray-900">{domain.id}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{domain.name}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{domain.totalControls}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{domain.totalSpecs}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Controls Preview */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-100 px-4 py-2 font-medium">{isRTL ? 'الضوابط (تعرض أول 5)' : 'Controls (Showing first 5)'}</div>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'الاسم' : 'Name'}</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'رقم المجال' : 'Domain ID'}</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'المواصفات' : 'Specs'}</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {processedData.controls.slice(0, 5).map((control) => (
                            <tr key={control.id}>
                              <td className="px-4 py-2 text-sm text-gray-900">{control.id}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{control.name}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{control.domainId}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{control.totalSpecs}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Specifications Preview */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-100 px-4 py-2 font-medium">{isRTL ? 'المواصفات (تعرض أول 5)' : 'Specifications (Showing first 5)'}</div>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'الاسم' : 'Name'}</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'رقم الضابط' : 'Control ID'}</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'رقم المجال' : 'Domain ID'}</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{isRTL ? 'الأولوية' : 'Priority'}</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {processedData.specifications.slice(0, 5).map((spec) => (
                            <tr key={spec.id}>
                              <td className="px-4 py-2 text-sm text-gray-900">{spec.id}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{spec.name}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{spec.controlId}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{spec.domainId}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{spec.priority}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 border-t border-gray-200 flex justify-end space-x-4">
                <button
                  onClick={() => setShowPreviewModal(false)}
                  className="px-4 py-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                >
                  {isRTL ? 'إلغاء' : 'Cancel'}
                </button>
                <button
                  onClick={handleSaveToFirebase}
                  disabled={isSaving}
                  className={`px-4 py-2 rounded-md ${isSaving ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'} text-white font-medium flex items-center`}
                >
                  {isSaving ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {isRTL ? 'جاري الحفظ...' : 'Saving...'}
                    </>
                  ) : (
                    <>{isRTL ? 'حفظ في فايربيس' : 'Save to Firebase'}</>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
