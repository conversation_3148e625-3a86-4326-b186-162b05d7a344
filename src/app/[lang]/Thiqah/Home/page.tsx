"use client";

import React, { useEffect, useState } from "react";
import { auth } from "@/Firebase/Authentication/authConfig";
import { Locale } from "@/i18n-config";
import { usePathname } from "next/navigation";
import { FuturisticHero } from "@/components/ui/FuturisticHero";

export default function HomePage() {
  const [userName, setUserName] = useState<string | null>(null);
  const pathname = usePathname();
  const lang = pathname?.split('/')[1] as Locale;

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        setUserName(user.displayName || user.email?.split('@')[0] || 'User');
      }
    });

    return () => unsubscribe();
  }, []);

  return (
    <FuturisticHero userName={userName} lang={lang} />
  );
}
