"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import { Plus, Shield, Database, Users, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemCard } from "@/components/ui/SystemCard";
import { AddSystemModal } from "@/components/ui/AddSystemModal";
import { SystemsService, System } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";

interface DataClassificationPageProps {
  params: { lang: Locale };
}

export default function DataClassificationPage({ params }: DataClassificationPageProps) {
  const { lang } = params;
  const isRTL = lang === "ar";
  const { toast } = useToast();

  const [systems, setSystems] = useState<System[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load systems on component mount
  useEffect(() => {
    loadSystems();
  }, []);

  const loadSystems = async () => {
    try {
      setIsLoading(true);
      const systemsData = await SystemsService.getSystems();
      setSystems(systemsData);
    } catch (error) {
      toast({
        title: isRTL ? "خطأ في تحميل الأنظمة" : "Error loading systems",
        description: isRTL ? "فشل في تحميل الأنظمة" : "Failed to load systems",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddSystem = async (systemData: {
    name: string;
    responsibleOwner: string;
    dba: string;
    email: string;
  }) => {
    try {
      setIsSubmitting(true);
      await SystemsService.addSystem(systemData);
      await loadSystems(); // Reload systems after adding
      setIsModalOpen(false);
      toast({
        title: isRTL ? "تم إضافة النظام بنجاح" : "System added successfully",
        description: isRTL ? "تم حفظ النظام الجديد" : "New system has been saved",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: isRTL ? "خطأ في إضافة النظام" : "Error adding system",
        description: isRTL ? "فشل في حفظ النظام" : "Failed to save system",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteSystem = async (systemId: string) => {
    if (window.confirm(isRTL ? "هل أنت متأكد من حذف هذا النظام؟" : "Are you sure you want to delete this system?")) {
      try {
        await SystemsService.deleteSystem(systemId);
        await loadSystems(); // Reload systems after deletion
        toast({
          title: isRTL ? "تم حذف النظام" : "System deleted",
          description: isRTL ? "تم حذف النظام بنجاح" : "System has been deleted successfully",
          variant: "default",
        });
      } catch (error) {
        toast({
          title: isRTL ? "خطأ في حذف النظام" : "Error deleting system",
          description: isRTL ? "فشل في حذف النظام" : "Failed to delete system",
          variant: "destructive",
        });
      }
    }
  };

  const stats = [
    {
      title: isRTL ? "إجمالي الأنظمة" : "Total Systems",
      value: systems.length,
      icon: <Database className="w-6 h-6" />,
    },
    {
      title: isRTL ? "المسؤولون" : "Responsible Owners",
      value: new Set(systems.map(s => s.responsibleOwner)).size,
      icon: <Users className="w-6 h-6" />,
    },
    {
      title: isRTL ? "مديرو قواعد البيانات" : "Database Administrators",
      value: new Set(systems.map(s => s.dba)).size,
      icon: <Shield className="w-6 h-6" />,
    },
    {
      title: isRTL ? "معدل النمو" : "Growth Rate",
      value: systems.length > 0 ? "+" + Math.round((systems.length / 10) * 100) + "%" : "0%",
      icon: <TrendingUp className="w-6 h-6" />,
    },
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Hero Section - Full Width */}
      <div className="relative min-h-[85vh] bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
          <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-white rounded-full opacity-50"></div>
        </div>

        <div className="relative z-10 flex flex-col justify-center items-center min-h-[85vh] px-8 py-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-6xl mx-auto"
          >
            {/* Main Header */}
            <div className="flex items-center justify-center gap-6 mb-8">
              <motion.div 
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="w-20 h-20 md:w-24 md:h-24 bg-white/20 backdrop-blur-sm rounded-3xl flex items-center justify-center shadow-2xl border border-white/30"
              >
                <Shield className="w-10 h-10 md:w-12 md:h-12 text-white" />
              </motion.div>
              <div className="text-left">
                <motion.h1 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="text-5xl md:text-7xl font-bold text-white mb-2 tracking-tight"
                >
                  {isRTL ? "تصنيف البيانات" : "Data Classification"}
                </motion.h1>
                <motion.p 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="text-xl md:text-2xl text-white/90 font-medium"
                >
                  {isRTL ? "إدارة أنظمة البيانات المؤسسية" : "Enterprise Data Systems Management"}
                </motion.p>
              </div>
            </div>

            {/* Description */}
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="text-lg md:text-xl text-white/85 max-w-4xl mx-auto mb-12 leading-relaxed font-light"
            >
              {isRTL 
                ? "منصة متطورة وشاملة لإدارة وتصنيف أنظمة البيانات في مؤسستك. استفد من أدوات قوية لإضافة الأنظمة، تعيين المسؤولين، وإدارة قواعد البيانات بأحدث التقنيات والمعايير الأمنية."
                : "An advanced and comprehensive platform for managing and classifying data systems within your organization. Leverage powerful tools to add systems, assign responsible owners, and manage databases with cutting-edge technology and security standards."
              }
            </motion.p>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1 }}
              className="mb-16"
            >
              <Button
                onClick={() => setIsModalOpen(true)}
                size="lg"
                className="bg-white text-[var(--brand-blue)] hover:bg-white/90 font-bold px-12 py-4 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 text-lg border-0"
              >
                <Plus className="w-6 h-6 mr-3" />
                {isRTL ? "إضافة نظام جديد" : "Add New System"}
              </Button>
            </motion.div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8 max-w-5xl mx-auto"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
                  className="bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 group"
                >
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="p-3 rounded-xl bg-white/20 text-white group-hover:bg-white/30 transition-all duration-300">
                        {stat.icon}
                      </div>
                    </div>
                    <p className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.value}</p>
                    <p className="text-sm md:text-base text-white/80 font-medium">{stat.title}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Floating Elements */}
        <motion.div
          animate={{ 
            y: [0, -20, 0],
            rotate: [0, 5, 0]
          }}
          transition={{ 
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-20 right-20 w-16 h-16 bg-white/10 rounded-full backdrop-blur-sm border border-white/20 hidden lg:block"
        />
        <motion.div
          animate={{ 
            y: [0, 15, 0],
            rotate: [0, -5, 0]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute bottom-32 left-16 w-12 h-12 bg-white/10 rounded-full backdrop-blur-sm border border-white/20 hidden lg:block"
        />
      </div>

      {/* Systems Section */}
      <div className="px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          {/* Section Header */}
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="inline-flex items-center gap-3 bg-[var(--brand-blue)]/10 px-6 py-3 rounded-full mb-6"
            >
              <Database className="w-5 h-5 text-[var(--brand-blue)]" />
              <span className="text-[var(--brand-blue)] font-semibold">
                {isRTL ? "إدارة الأنظمة" : "Systems Management"}
              </span>
            </motion.div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--brand-dark-gray)] mb-4">
              {isRTL ? "الأنظمة المسجلة" : "Registered Systems"}
            </h2>
            
            <div className="flex items-center justify-center gap-2 text-gray-600">
              <span className="text-2xl font-bold text-[var(--brand-blue)]">{systems.length}</span>
              <span>
                {isRTL 
                  ? `${systems.length === 1 ? 'نظام مسجل' : 'أنظمة مسجلة'}`
                  : `${systems.length === 1 ? 'System Registered' : 'Systems Registered'}`
                }
              </span>
            </div>
          </div>

          {isLoading ? (
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[...Array(6)].map((_, index) => (
                  <div
                    key={index}
                    className="bg-white/50 animate-pulse rounded-2xl h-72 border border-gray-200/50"
                  />
                ))}
              </div>
            </div>
          ) : systems.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-20"
            >
              <div className="max-w-md mx-auto">
                <div className="w-32 h-32 bg-gradient-to-br from-[var(--brand-blue)]/20 to-[var(--brand-blue)]/10 rounded-full flex items-center justify-center mx-auto mb-8">
                  <Database className="w-16 h-16 text-[var(--brand-blue)]" />
                </div>
                <h3 className="text-2xl font-bold text-[var(--brand-dark-gray)] mb-4">
                  {isRTL ? "لا توجد أنظمة مسجلة" : "No Systems Registered"}
                </h3>
                <p className="text-gray-600 mb-8 text-lg">
                  {isRTL 
                    ? "ابدأ رحلتك في إدارة البيانات بإضافة أول نظام لك"
                    : "Start your data management journey by adding your first system"
                  }
                </p>
                <Button
                  onClick={() => setIsModalOpen(true)}
                  size="lg"
                  className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  {isRTL ? "إضافة نظام جديد" : "Add New System"}
                </Button>
              </div>
            </motion.div>
          ) : (
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {systems.map((system, index) => (
                  <motion.div
                    key={system.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <SystemCard
                      system={system}
                      lang={lang}
                      onDelete={handleDeleteSystem}
                    />
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* Add System Modal */}
      <AddSystemModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleAddSystem}
        lang={lang}
        isLoading={isSubmitting}
      />
    </div>
  );
} 