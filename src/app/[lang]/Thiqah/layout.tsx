import React from "react";
import { type Locale } from '@/i18n-config';
import ThiqahLayoutClient from "./ThiqahLayoutClient";

interface ThiqahLayoutProps {
  children: React.ReactNode;
  params: { lang: Locale };
}

export default async function ThiqahLayout({ children, params }: ThiqahLayoutProps) {
  // Access params after awaiting in server component
  const { lang } = await params;
  
  // Return the client component with the props it needs
  return (
    <div style={{ backgroundColor: 'var(--brand-blue)', minHeight: '100vh' }}>
      <ThiqahLayoutClient lang={lang}>
        {children}
      </ThiqahLayoutClient>
    </div>
  );
}
