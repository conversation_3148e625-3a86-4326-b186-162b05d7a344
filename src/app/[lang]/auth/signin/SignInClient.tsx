"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";
import { AuthForm } from "@/components/ui/authUI/AuthForm";
import { FormField } from "@/components/ui/authUI/FormField";
import { loginWithEmailAndPassword, signInWithGoogle } from "@/Firebase/Authentication/authConfig";
import { upsertUserProfile } from "@/Firebase/firestore/services/UserService";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { i18n, type Locale } from '@/i18n-config';
import { Button } from "@/components/ui/button";

// Language switcher component
const LanguageSwitcher = () => {
  const pathname = usePathname();

  const getPathWithLocale = (locale: Locale) => {
    if (!pathname) return '/';
    const segments = pathname.split('/');
    segments[1] = locale;
    return segments.join('/');
  };

  return (
    <div className="flex gap-2">
      {i18n.locales.map((locale) => {
        const isActive = pathname.split('/')[1] === locale;
        return (
          <Link key={locale} href={getPathWithLocale(locale)}>
            <Button variant={isActive ? "secondary" : "ghost"} size="sm">
              {locale.toUpperCase()}
            </Button>
          </Link>
        );
      })}
    </div>
  );
};

// Animation background component
const AnimatedBackground = () => {
  return (
    <div className="relative w-full h-full overflow-hidden bg-gradient-to-br from-[#003366] via-[#004080] to-[#0066b3]">
      {/* Animated circles */}
      <div className="absolute top-0 left-0 w-full h-full">
        {[...Array(10)].map((_, i) => (
          <div 
            key={i}
            className="absolute rounded-full opacity-20 animate-float"
            style={{
              width: `${Math.random() * 300 + 50}px`,
              height: `${Math.random() * 300 + 50}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              backgroundColor: `rgba(255, 255, 255, ${Math.random() * 0.5})`,
              animationDuration: `${Math.random() * 10 + 10}s`,
              animationDelay: `${Math.random() * 5}s`
            }}
          />
        ))}
      </div>
      
      {/* Branding */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-white z-10">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">THIqah COnsultant Helper</h1>
        <p className="text-xl md:text-2xl text-center max-w-lg opacity-80">Your trusted consultant helper for all your business needs</p>
      </div>
    </div>
  );
};

export default function SignInClient({
  dict
}: {
  dict: Dictionary
}) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Add animation CSS
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes float {
        0% { transform: translate(0, 0) rotate(0deg); opacity: 0.2; }
        50% { transform: translate(30px, 30px) rotate(180deg); opacity: 0.5; }
        100% { transform: translate(0, 0) rotate(360deg); opacity: 0.2; }
      }
      .animate-float {
        animation: float 15s infinite ease-in-out;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Form schema
  const formSchema = z.object({
    email: z.string().email({
      message: dict.auth.common.invalidEmail,
    }),
    password: z.string().min(1, {
      message: dict.auth.common.requiredField,
    }),
    rememberMe: z.boolean().optional(),
  });

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      // Sign in with email and password
      const user = await loginWithEmailAndPassword(data.email, data.password);

      // Create or update user profile in Firestore
      await upsertUserProfile(user.uid, {
        email: user.email || "",
        displayName: user.displayName || "",
        photoURL: user.photoURL,
      });

      toast({
        title: dict.auth.signin.success,
        variant: "default",
      });
      router.push("success");
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signin.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      // Sign in with Google and get the user object
      const user = await signInWithGoogle();

      // Create or update user profile in Firestore
      await upsertUserProfile(user.uid, {
        email: user.email || "",
        displayName: user.displayName || "",
        photoURL: user.photoURL,
      });

      toast({
        title: dict.auth.signin.success,
        variant: "default",
      });
      router.push("success");
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signin.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex h-screen w-full overflow-hidden">
      {/* 70% Animated Background */}
      <div className="hidden md:block w-[70%] h-full">
        <AnimatedBackground />
      </div>
      
      {/* 30% Sign In Form */}
      <div className="w-full md:w-[30%] h-full bg-background flex flex-col justify-start items-center p-6 overflow-y-auto">
        {/* Language switcher at the top */}
        <div className="w-full flex justify-end mb-8">
          <LanguageSwitcher />
        </div>
        
        {/* Logo */}
        <div className="mb-8 flex justify-center">
          <Image 
            src="/image.png" 
            alt="THIqah Logo" 
            width={150} 
            height={150} 
            className="mx-auto"
            priority
          />
        </div>
        
        <div className="w-full max-w-md">
          <h2 className="text-2xl font-bold mb-2 text-center text-[#003366]">{dict.auth.signin.title}</h2>
          <p className="text-muted-foreground text-center mb-6">{dict.auth.signin.subtitle}</p>
          
          <AuthForm
            schema={formSchema}
            onSubmit={onSubmit}
            submitText={dict.auth.signin.button}
            isLoading={isLoading}
            googleSignIn={handleGoogleSignIn}
            googleText={dict.auth.common.continueWithGoogle}
          >
            <FormField
              name="email"
              label={dict.auth.common.email}
              placeholder="<EMAIL>"
              type="email"
              required
              autoComplete="email"
            />
            <FormField
              name="password"
              label={dict.auth.common.password}
              placeholder="••••••••"
              type="password"
              required
              autoComplete="current-password"
            />
            <div className="flex items-center justify-between">
              <FormField
                name="rememberMe"
                isCheckbox
                checkboxLabel={dict.auth.common.rememberMe}
              />
              <Link
                href="reset"
                className="text-sm font-medium text-[#003366] hover:underline"
              >
                {dict.auth.common.forgotPassword}
              </Link>
            </div>
          </AuthForm>
          
          <div className="text-center mt-6">
            <p className="text-sm text-muted-foreground">
              {dict.auth.common.dontHaveAccount}{" "}
              <Link
                href="signup"
                className="text-[#003366] underline underline-offset-4 hover:text-[#0066b3]"
              >
                {dict.auth.signup.title}
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
