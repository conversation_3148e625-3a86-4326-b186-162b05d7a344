"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { auth, sendUserEmailVerification, logoutUser } from "@/Firebase/Authentication/authConfig";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { i18n, type Locale } from '@/i18n-config';

// Language switcher component
const LanguageSwitcher = () => {
  const pathname = usePathname();

  const getPathWithLocale = (locale: Locale) => {
    if (!pathname) return '/';
    const segments = pathname.split('/');
    segments[1] = locale;
    return segments.join('/');
  };

  return (
    <div className="flex gap-2">
      {i18n.locales.map((locale) => {
        const isActive = pathname.split('/')[1] === locale;
        return (
          <Link key={locale} href={getPathWithLocale(locale)}>
            <Button variant={isActive ? "secondary" : "ghost"} size="sm">
              {locale.toUpperCase()}
            </Button>
          </Link>
        );
      })}
    </div>
  );
};

// Animation background component
const AnimatedBackground = () => {
  return (
    <div className="relative w-full h-full overflow-hidden bg-gradient-to-br from-[#003366] via-[#004080] to-[#0066b3]">
      {/* Animated circles */}
      <div className="absolute top-0 left-0 w-full h-full">
        {[...Array(10)].map((_, i) => (
          <div 
            key={i}
            className="absolute rounded-full opacity-20 animate-float"
            style={{
              width: `${Math.random() * 300 + 50}px`,
              height: `${Math.random() * 300 + 50}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              backgroundColor: `rgba(255, 255, 255, ${Math.random() * 0.5})`,
              animationDuration: `${Math.random() * 10 + 10}s`,
              animationDelay: `${Math.random() * 5}s`
            }}
          />
        ))}
      </div>
      
      {/* Branding */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-white z-10">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">THIqah COnsultant Helper</h1>
        <p className="text-xl md:text-2xl text-center max-w-lg opacity-80">Your trusted consultant helper for all your business needs</p>
      </div>
    </div>
  );
};

export default function VerifyClient({
  dict
}: {
  dict: Dictionary
}) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  
  // Add animation CSS
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes float {
        0% { transform: translate(0, 0) rotate(0deg); opacity: 0.2; }
        50% { transform: translate(30px, 30px) rotate(180deg); opacity: 0.5; }
        100% { transform: translate(0, 0) rotate(360deg); opacity: 0.2; }
      }
      .animate-float {
        animation: float 15s infinite ease-in-out;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    // Check if user is logged in
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        setEmail(user.email);

        // If email is already verified, redirect to success page
        if (user.emailVerified) {
          router.push("success");
        }
      } else {
        // If no user is logged in, redirect to sign in
        router.push("signin");
      }
    });

    return () => unsubscribe();
  }, [router]);

  useEffect(() => {
    // Countdown for resend button
    if (countdown > 0 && !canResend) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
  }, [countdown, canResend]);

  const handleResendEmail = async () => {
    try {
      setIsLoading(true);
      await sendUserEmailVerification();
      toast({
        title: dict.auth.verify.success,
        variant: "default",
      });
      setCanResend(false);
      setCountdown(60);
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.verify.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await logoutUser();
      router.push("signin");
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: "Error signing out",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex h-screen w-full overflow-hidden">
      {/* 70% Animated Background */}
      <div className="hidden md:block w-[70%] h-full">
        <AnimatedBackground />
      </div>
      
      {/* 30% Verify Email Form */}
      <div className="w-full md:w-[30%] h-full bg-background flex flex-col justify-start items-center p-6 overflow-y-auto">
        {/* Language switcher at the top */}
        <div className="w-full flex justify-end mb-8">
          <LanguageSwitcher />
        </div>
        
        {/* Logo */}
        <div className="mb-8 flex justify-center">
          <Image 
            src="/image.png" 
            alt="THIqah Logo" 
            width={150} 
            height={150} 
            className="mx-auto"
            priority
          />
        </div>
        
        <div className="w-full max-w-md">
          <h2 className="text-2xl font-bold mb-2 text-center text-[#003366]">{dict.auth.verify.title}</h2>
          <p className="text-muted-foreground text-center mb-6">{dict.auth.verify.subtitle}</p>
          
          <div className="space-y-6">
            {email && (
              <div className="bg-[#f0f5fa] border border-[#003366]/20 p-4 rounded-md text-center">
                <p className="text-sm font-medium text-[#003366]">{email}</p>
              </div>
            )}

            <p className="text-sm text-center text-muted-foreground">
              {dict.auth.verify.checkInbox}
            </p>

            <div className="space-y-3">
              <Button
                onClick={handleResendEmail}
                variant="outline"
                className="w-full border-[#003366] text-[#003366] hover:bg-[#f0f5fa] hover:text-[#004080]"
                disabled={isLoading || !canResend}
              >
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    {dict.auth.verify.resendEmail}
                  </span>
                ) : canResend ? (
                  dict.auth.verify.resendEmail
                ) : (
                  `${dict.auth.verify.resendEmail} (${countdown}s)`
                )}
              </Button>

              <Button
                onClick={() => {
                  try {
                    // Reload the user to check if email is verified
                    auth.currentUser?.reload().then(() => {
                      if (auth.currentUser?.emailVerified) {
                        // If email is verified, redirect to success page
                        router.push("success");
                      } else {
                        // Otherwise, just reload the page
                        window.location.reload();
                      }
                    }).catch(error => {
                      const errorMessage = handleAuthError(error);
                      toast({
                        title: dict.auth.verify.error,
                        description: errorMessage,
                        variant: "destructive",
                      });
                    });
                  } catch (error) {
                    const errorMessage = handleAuthError(error);
                    toast({
                      title: dict.auth.verify.error,
                      description: errorMessage,
                      variant: "destructive",
                    });
                  }
                }}
                className="w-full bg-[#003366] hover:bg-[#004080] text-white"
              >
                {dict.auth.verify.button}
              </Button>
            </div>

            <div className="text-center mt-6">
              <button
                onClick={handleSignOut}
                className="text-[#003366] underline underline-offset-4 hover:text-[#0066b3]"
              >
                {dict.auth.reset.backToSignIn}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
