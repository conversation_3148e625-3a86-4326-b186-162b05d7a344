import React from "react";
import { Locale } from "@/i18n-config";
import { getDictionary } from "@/dictionaries";
import { Toaster } from "@/components/ui/toaster";

export default async function AuthLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: Locale }>;
}) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <>
      {children}
      {/* Toast notifications */}
      <Toaster />
    </>
  );
}
