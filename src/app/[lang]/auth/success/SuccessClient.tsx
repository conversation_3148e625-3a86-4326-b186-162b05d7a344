"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AuthCard } from "@/components/ui/authUI/AuthCard";
import { Button } from "@/components/ui/button";
import { auth, logoutUser } from "@/Firebase/Authentication/authConfig";
import { getUserProfile, upsertUserProfile } from "@/Firebase/firestore/services/UserService";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { NameMeaningCard } from "@/components/app/[lang]/auth/success/NameMeaningCard";
import { MuscleTrainingCard } from "@/components/app/[lang]/auth/success/MuscleTrainingCard";
import { ImageDescriptionCard } from "@/components/app/[lang]/auth/success/ImageDescriptionCard";

// Helper function to format Firestore timestamp or Date object
const formatDate = (dateOrTimestamp: unknown): string => {
  if (!dateOrTimestamp) return '';

  // Check if it's a Firestore Timestamp (has toDate method)
  if (dateOrTimestamp &&
      typeof dateOrTimestamp === 'object' &&
      'toDate' in dateOrTimestamp &&
      typeof (dateOrTimestamp as { toDate: () => Date }).toDate === 'function') {
    return (dateOrTimestamp as { toDate: () => Date }).toDate().toLocaleDateString();
  }

  // Check if it's a Date object
  if (dateOrTimestamp instanceof Date) {
    return dateOrTimestamp.toLocaleDateString();
  }

  // If it's a timestamp number
  if (typeof dateOrTimestamp === 'number') {
    return new Date(dateOrTimestamp).toLocaleDateString();
  }

  // If it's an object with seconds (Firestore timestamp format)
  if (dateOrTimestamp && typeof dateOrTimestamp === 'object' && 'seconds' in dateOrTimestamp &&
      typeof (dateOrTimestamp as { seconds: number }).seconds === 'number') {
    return new Date((dateOrTimestamp as { seconds: number }).seconds * 1000).toLocaleDateString();
  }

  // Fallback
  return String(dateOrTimestamp);
};

export default function SuccessClient({
  dict
}: {
  dict: Dictionary
}) {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState<{
    uid: string;
    email: string | null;
    displayName: string | null;
    photoURL: string | null;
    emailVerified: boolean;
    role?: string;
    createdAt?: Date;
  } | null>(null);

  useEffect(() => {
    // Check if user is logged in
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      if (user) {
        try {
          // Get additional user data from Firestore
          let userProfile = await getUserProfile(user.uid);

          // If no user profile exists in Firestore, create one
          if (!userProfile) {
            await upsertUserProfile(user.uid, {
              email: user.email || "",
              displayName: user.displayName || "",
              photoURL: user.photoURL,
            });

            // Fetch the newly created profile
            userProfile = await getUserProfile(user.uid);
          }

          setUserData({
            uid: user.uid,
            email: user.email,
            displayName: user.displayName || userProfile?.displayName || null,
            photoURL: user.photoURL || userProfile?.photoURL || null,
            emailVerified: user.emailVerified,
            role: userProfile?.role,
            createdAt: userProfile?.createdAt,
          });
        } catch (error) {
          console.error("Error fetching user profile:", error);
          // Still set basic user data even if Firestore fetch fails
          setUserData({
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
            photoURL: user.photoURL,
            emailVerified: user.emailVerified,
          });
        } finally {
          setLoading(false);
        }
      } else {
        // If no user is logged in, redirect to sign in
        router.push("signin");
      }
    });

    return () => unsubscribe();
  }, [router]);

  const handleSignOut = async () => {
    try {
      await logoutUser();
      toast({
        title: dict.auth.success.signOutSuccess,
        variant: "default",
      });
      router.push("signin");
    } catch (error) {
      console.error("Error signing out:", error);
      toast({
        title: dict.auth.success.signOutError,
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[300px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full max-w-md mx-auto">
      <AuthCard
        title={dict.auth.success.title}
        description={dict.auth.success.subtitle}
      >
        <div className="flex flex-col items-center space-y-4 mb-6">
          <Avatar className="h-24 w-24">
            {userData?.photoURL ? (
              <AvatarImage src={userData.photoURL} alt={userData.displayName || "User"} />
            ) : (
              <AvatarFallback className="text-2xl">
                {userData?.displayName ? userData.displayName.charAt(0).toUpperCase() :
                 userData?.email ? userData.email.charAt(0).toUpperCase() : "U"}
              </AvatarFallback>
            )}
          </Avatar>
          <h2 className="text-xl font-bold">{userData?.displayName || "User"}</h2>
          <p className="text-muted-foreground">{userData?.email}</p>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">{dict.auth.success.accountInfo}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">{dict.auth.success.userId}:</span>
                <span className="font-medium truncate max-w-[200px]">{userData?.uid}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">{dict.auth.success.emailVerified}:</span>
                <span className={`font-medium ${userData?.emailVerified ? 'text-green-500' : 'text-amber-500'}`}>
                  {userData?.emailVerified ? dict.auth.success.emailVerifiedYes : dict.auth.success.emailVerifiedNo}
                </span>
              </div>
              {userData?.role && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{dict.auth.success.role}:</span>
                  <span className="font-medium capitalize">{userData.role}</span>
                </div>
              )}
              {userData?.createdAt && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{dict.auth.success.joined}:</span>
                  <span className="font-medium">
                    {formatDate(userData.createdAt)}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Render the Name Meaning Card if displayName exists */}
          {userData?.displayName && (
            <NameMeaningCard displayName={userData.displayName} dict={dict} />
          )}

          {/* Render the Muscle Training Card */}
          <MuscleTrainingCard dict={dict} />

          {/* Render the Image Description Card */}
          <ImageDescriptionCard dict={dict} />

          <div className="flex flex-col space-y-2">
            {!userData?.emailVerified && (
              <Button
                variant="outline"
                onClick={() => router.push("verify")}
                className="w-full"
              >
                {dict.auth.success.verifyEmailButton}
              </Button>
            )}
            <Button
              variant="default"
              onClick={handleSignOut}
              className="w-full"
            >
              {dict.auth.success.signOutButton}
            </Button>
          </div>
        </div>
      </AuthCard>
    </div>
  );
}
