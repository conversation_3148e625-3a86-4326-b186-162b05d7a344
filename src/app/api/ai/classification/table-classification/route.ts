import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 120 seconds for complex table analysis
export const maxDuration = 120;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for table classification response
const TableClassificationSchema = z.object({
  tables: z.array(z.object({
    tableName: z.string(),
    schemaName: z.string().optional(),
    tableType: z.enum(["system_table", "data_table"]),
    dataCategory: z.enum(["customers", "development_team"]),
    reasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  systemData: z.array(z.object({
    id: z.string().optional(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional()
  })),
  systemId: z.string()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    console.log('Received request body:', {
      systemDataLength: body.systemData?.length,
      systemId: body.systemId,
      sampleData: body.systemData?.slice(0, 2) // Log first 2 records for debugging
    });

    const { systemData, systemId } = RequestSchema.parse(body);

    if (!systemData || systemData.length === 0) {
      return NextResponse.json(
        { error: 'No system data provided for classification' },
        { status: 400 }
      );
    }



    // Extract unique tables from system data
    const uniqueTables = Array.from(
      new Map(
        systemData.map(item => [
          `${item.schemaName || 'default'}.${item.tableName}`,
          {
            tableName: item.tableName,
            schemaName: item.schemaName,
            columns: systemData
              .filter(d => d.tableName === item.tableName && d.schemaName === item.schemaName)
              .map(d => ({
                columnName: d.columnName,
                dataType: d.dataType,
                maxLength: d.maxLength,
                isNullable: d.isNullable
              }))
          }
        ])
      ).values()
    );

    // Limit to first 10 tables to avoid token limits
    const tablesToProcess = uniqueTables.slice(0, 10);
    console.log(`Processing ${tablesToProcess.length} tables out of ${uniqueTables.length} total tables`);

    // Prepare prompt for AI classification
    const prompt = `Classify these database tables:

${tablesToProcess.map(table => `${table.tableName}`).join('\n')}

For each table, determine:
- tableType: "system_table" (config/logs/metadata) or "data_table" (business data)
- dataCategory: "customers" (user data) or "development_team" (technical data)

Return JSON: {"tables": [{"tableName": "...", "tableType": "...", "dataCategory": "...", "reasoning": "..."}]}`;

    console.log('Sending prompt to AI for', tablesToProcess.length, 'tables');

    // Use model for table analysis
    const result = await generateObject({
      model: google('gemini-1.5-flash'),
      prompt: prompt,
      schema: TableClassificationSchema,
      maxTokens: 8000,
    });

    console.log('AI response received:', result.object);

    // For tables not processed, assign default classifications
    const allTableClassifications = [];

    // Add AI-classified tables
    allTableClassifications.push(...result.object.tables);

    // Add default classifications for remaining tables
    const processedTableNames = new Set(result.object.tables.map(t => t.tableName));
    uniqueTables.forEach(table => {
      if (!processedTableNames.has(table.tableName)) {
        // Default classification logic
        const isSystemTable = table.tableName.toLowerCase().includes('log') ||
                             table.tableName.toLowerCase().includes('audit') ||
                             table.tableName.toLowerCase().includes('setting') ||
                             table.tableName.toLowerCase().includes('config') ||
                             table.tableName.toLowerCase().includes('migration') ||
                             table.tableName.toLowerCase().includes('aspnet') ||
                             table.tableName.toLowerCase().includes('__ef');

        allTableClassifications.push({
          tableName: table.tableName,
          schemaName: table.schemaName,
          tableType: isSystemTable ? "system_table" : "data_table",
          dataCategory: isSystemTable ? "development_team" : "customers",
          reasoning: "Auto-classified based on table name patterns (not processed by AI due to volume limits)"
        });
      }
    });

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'table_classification',
      systemId,
      classifications: allTableClassifications,
      totalTables: uniqueTables.length,
      aiProcessedTables: result.object.tables.length,
      autoClassifiedTables: allTableClassifications.length - result.object.tables.length,
      usage: result.usage
    });

  } catch (error) {
    console.error('Error in table classification:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined
    });

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error during table classification',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
