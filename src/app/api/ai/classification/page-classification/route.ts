import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 60 seconds for page analysis
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for page classification response
const PageClassificationSchema = z.object({
  records: z.array(z.object({
    recordId: z.string(),
    confidentialityLevel: z.enum(["Public", "Confidential", "Secret", "Top Secret"]),
    confidentialityReasoning: z.string(),
    hasPersonalData: z.boolean(),
    personalDataReasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  pageData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional(),
    tableType: z.enum(["system_table", "data_table"]).optional(),
    dataCategory: z.enum(["customers", "development_team"]).optional()
  })),
  systemId: z.string(),
  pageNumber: z.number(),
  startIndex: z.number(),
  endIndex: z.number(),
  systemContext: z.string().optional()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { pageData, systemId, pageNumber, startIndex, endIndex, systemContext } = RequestSchema.parse(body);

    if (!pageData || pageData.length === 0) {
      return NextResponse.json(
        { error: 'No page data provided for classification' },
        { status: 400 }
      );
    }

    // Get actual SABER business context from the system
    console.log('System context received:', systemContext ? `"${systemContext}"` : 'null/undefined');
    let saberBusinessContext = '';
    if (systemContext && systemContext.trim()) {
      saberBusinessContext = `

ACTUAL SABER BUSINESS CONTEXT FOR THIS SYSTEM:
${systemContext}

This context describes the specific SABER business functions and processes that this database supports.`;
      console.log('Using Firebase system context in AI prompt');
    } else {
      console.log('No system context provided - using generic SABER context');
    }

    // Prepare prompt for AI classification with actual SABER context
    const prompt = `You are classifying database fields for the SABER platform - Saudi Arabia's national conformity assessment system.

SABER Platform Overview:
- SABER manages product certificates, facility registrations, and compliance requests
- Handles sensitive business data for importers, manufacturers, and certification bodies
- Processes regulatory compliance documents and technical regulations
- Manages user accounts for facilities, organizations, and government entities
- Stores financial data for fees, bills, and payments through SADAD system
- Contains audit trails for regulatory compliance and government oversight${saberBusinessContext}

Based on the table names in this system, here are the key SABER business entities:
${Array.from(new Set(pageData.map(record => record.tableName))).map(tableName => {
  // Provide specific SABER business context for each table
  if (tableName.toLowerCase().includes('certificate')) return `- ${tableName}: Manages SABER product certificates and compliance documentation`;
  if (tableName.toLowerCase().includes('request')) return `- ${tableName}: Handles SABER compliance requests and applications`;
  if (tableName.toLowerCase().includes('facility')) return `- ${tableName}: Manages facility registrations and business information`;
  if (tableName.toLowerCase().includes('organization')) return `- ${tableName}: Handles certification body and organization data`;
  if (tableName.toLowerCase().includes('bill') || tableName.toLowerCase().includes('payment')) return `- ${tableName}: Manages SABER fees and SADAD payment processing`;
  if (tableName.toLowerCase().includes('attachment')) return `- ${tableName}: Stores regulatory documents and compliance attachments`;
  if (tableName.toLowerCase().includes('user') || tableName.toLowerCase().includes('aspnet')) return `- ${tableName}: Manages SABER platform user accounts and authentication`;
  if (tableName.toLowerCase().includes('audit') || tableName.toLowerCase().includes('log')) return `- ${tableName}: Tracks SABER system activities and compliance audit trails`;
  return `- ${tableName}: SABER business data table`;
}).join('\n')}

Classify these ${pageData.length} fields with SHORT, SABER-specific reasoning:

${pageData.map(record => `${record.id}: ${record.tableName}.${record.columnName} (${record.dataType})`).join('\n')}

IMPORTANT: Keep all reasoning SHORT (1-2 sentences max).

For confidentialityLevel (Public/Confidential/Secret/Top Secret):
- Reference the specific SABER business function this table/field supports
- Explain impact on SABER operations if exposed (briefly)
- Keep reasoning under 50 words

For personalDataReasoning:
- Reference specific SABER user types (facility owners, organization officers, government users)
- Explain how data relates to SABER business processes (briefly)
- Keep reasoning under 30 words

JSON format: {"records": [{"recordId": "...", "confidentialityLevel": "...", "confidentialityReasoning": "...", "hasPersonalData": true/false, "personalDataReasoning": "..."}]}`;

    let processedRecords;
    let usage = null;

    try {
      console.log(`Sending prompt to AI: ${prompt}`);

      // Use gemini-2.5-flash for better classification with SABER context
      const result = await generateObject({
        model: google('gemini-2.5-flash'),
        prompt: prompt,
        schema: PageClassificationSchema,
        maxTokens: 8000,
      });

      console.log('AI response received:', result.object);
      processedRecords = result.object.records;
      usage = result.usage;

    } catch (aiError) {
      console.error('AI classification failed, using fallback rules:', aiError);

      // Fallback: Use SABER-aware rule-based classification with detailed reasoning
      processedRecords = pageData.map(record => {
        const columnName = record.columnName.toLowerCase();
        const tableName = record.tableName.toLowerCase();
        const dataType = record.dataType?.toLowerCase() || '';

        // SABER-specific confidentiality classification
        let confidentialityLevel = "Confidential";
        let confidentialityReasoning = `SABER ${record.tableName} contains regulatory compliance data requiring protection`;

        if (columnName.includes('id') && !columnName.includes('userid') && !columnName.includes('ownerid')) {
          confidentialityLevel = "Public";
          confidentialityReasoning = `SABER system identifier for linking certificates/requests/facilities`;
        } else if (columnName.includes('password') || columnName.includes('secret') || columnName.includes('token') || columnName.includes('hash')) {
          confidentialityLevel = "Top Secret";
          confidentialityReasoning = `SABER authentication credentials protecting regulatory compliance system`;
        } else if (columnName.includes('xml') || columnName.includes('path') || columnName.includes('file') || columnName.includes('key')) {
          confidentialityLevel = "Secret";
          confidentialityReasoning = `SABER system configuration exposing platform infrastructure`;
        } else if (columnName.includes('amount') || columnName.includes('price') || columnName.includes('cost') || columnName.includes('bill') || columnName.includes('sadad')) {
          confidentialityLevel = "Confidential";
          confidentialityReasoning = `SABER financial data for regulatory fees and SADAD payments`;
        } else if (columnName.includes('createdon') || columnName.includes('updatedon') || columnName.includes('date')) {
          confidentialityLevel = "Public";
          confidentialityReasoning = `SABER audit timestamps for certificate/request processing`;
        } else if (tableName.includes('certificate') || tableName.includes('request') || tableName.includes('facility')) {
          confidentialityLevel = "Confidential";
          confidentialityReasoning = `SABER regulatory data affecting product certification and market access`;
        }

        // SABER-specific personal data classification
        const hasPersonalData = columnName.includes('name') ||
                               columnName.includes('email') ||
                               columnName.includes('phone') ||
                               columnName.includes('mobile') ||
                               columnName.includes('username') ||
                               columnName.includes('owner') ||
                               columnName.includes('createdby') ||
                               columnName.includes('updatedby') ||
                               columnName.includes('user');

        let personalDataReasoning;
        if (hasPersonalData) {
          if (columnName.includes('email')) {
            personalDataReasoning = `SABER user emails for facility owners/organization officers`;
          } else if (columnName.includes('phone') || columnName.includes('mobile')) {
            personalDataReasoning = `SABER contact numbers for facility managers/representatives`;
          } else if (columnName.includes('name')) {
            personalDataReasoning = `SABER user names identifying facility owners/officers`;
          } else if (columnName.includes('username') || columnName.includes('user')) {
            personalDataReasoning = `SABER user identifiers linking to certification requests`;
          } else if (columnName.includes('createdby') || columnName.includes('updatedby') || columnName.includes('owner')) {
            personalDataReasoning = `SABER audit trail identifying users who modified records`;
          } else {
            personalDataReasoning = `SABER personal data identifying individuals in compliance processes`;
          }
        } else {
          if (columnName.includes('id') && !columnName.includes('userid')) {
            personalDataReasoning = `SABER system identifier - no personal data`;
          } else if (columnName.includes('date') || columnName.includes('time')) {
            personalDataReasoning = `SABER timestamps - no individual identification`;
          } else if (columnName.includes('status') || columnName.includes('type') || columnName.includes('flag')) {
            personalDataReasoning = `SABER workflow status - not linked to individuals`;
          } else if (columnName.includes('amount') || columnName.includes('count') || columnName.includes('number') || columnName.includes('sadad')) {
            personalDataReasoning = `SABER financial/numerical data - no personal identifiers`;
          } else {
            personalDataReasoning = `SABER business data - no individual personal information`;
          }
        }

        return {
          recordId: record.id,
          confidentialityLevel,
          confidentialityReasoning,
          hasPersonalData,
          personalDataReasoning
        };
      });

      // Set fallback usage info
      usage = {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      };
    }

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'page_classification',
      systemId,
      pageNumber,
      startIndex,
      endIndex,
      recordsProcessed: pageData.length,
      classifications: processedRecords,
      usage: usage
    });

  } catch (error) {
    console.error('Error in page classification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during page classification' },
      { status: 500 }
    );
  }
}
