import { NextRequest, NextResponse } from 'next/server';
// Use require to avoid TypeScript issues with the Google Generative AI SDK
// @ts-ignore
const { GoogleGenerativeAI } = require('@google/generative-ai');
import mime from 'mime';
import fs from 'fs';
import path from 'path';
import { getAllDomains, getControlsByDomain, getSpecificationsByControl } from '@/Firebase/firestore/services/NDMOService';

// Interface for Audio response
interface AudioResponse {
  url: string;
  fileName: string;
}

/**
 * Converts NDMO data to podcast script format
 * @param isRTL Whether to use RTL language (Arabic)
 * @param providedScript Optional script to use instead of generating one
 */
async function createPodcastScript(isRTL: boolean = false, providedScript?: string) {
  try {
    // If a script is provided, use it directly
    if (providedScript) {
      return providedScript;
    }
    
    // Otherwise, fetch domains and generate a script
    const domains = await getAllDomains();
    
    // Create a script introduction
    const intro = isRTL 
      ? "مرحبًا بكم في بودكاست المركز الوطني لإدارة البيانات. سنستعرض اليوم المجالات والضوابط والمواصفات المتاحة."
      : "Welcome to the National Data Management Office podcast. Today we will explore available domains, controls, and specifications.";
    
    let script = `Speaker 1: ${intro}\n\n`;
    
    // Add information about domains
    script += isRTL 
      ? `Speaker 1: لدينا ${domains.length} مجالات في النظام.\n`
      : `Speaker 1: We have ${domains.length} domains in the system.\n`;
    
    // Add detailed information about each domain
    for (const [index, domain] of domains.slice(0, 3).entries()) {
      // Get controls for this domain
      const controls = await getControlsByDomain(domain.id);
      
      const domainIntro = isRTL
        ? `Speaker 2: المجال رقم ${index + 1} هو "${domain.name}" ويحتوي على ${domain.totalControls} ضوابط و ${domain.totalSpecs} مواصفات.`
        : `Speaker 2: Domain number ${index + 1} is "${domain.name}" and contains ${domain.totalControls} controls and ${domain.totalSpecs} specifications.`;
      
      script += `${domainIntro}\n`;
      
      // Add information about a couple of controls from this domain
      if (controls.length > 0) {
        const control = controls[0];
        const controlInfo = isRTL
          ? `Speaker 1: من ضمن الضوابط في هذا المجال: "${control.name}".`
          : `Speaker 1: One of the controls in this domain is: "${control.name}".`;
        
        script += `${controlInfo}\n`;
        
        // Get specifications for this control
        const specs = await getSpecificationsByControl(domain.id, control.id);
        if (specs.length > 0) {
          const spec = specs[0];
          const specInfo = isRTL
            ? `Speaker 2: هذا الضابط يحتوي على مواصفات مثل: "${spec.name}" بأولوية ${spec.priority}.`
            : `Speaker 2: This control contains specifications like: "${spec.name}" with priority ${spec.priority}.`;
          
          script += `${specInfo}\n`;
        }
      }
      
      script += "\n";
    }
    
    // Add conclusion
    const conclusion = isRTL
      ? "Speaker 1: هذا كان استعراضًا موجزًا للمركز الوطني لإدارة البيانات. شكرًا للاستماع."
      : "Speaker 1: This was a brief overview of the National Data Management Office data. Thanks for listening.";
    
    script += conclusion;
    
    return script;
  } catch (error) {
    console.error("Error creating podcast script:", error);
    throw new Error("Failed to create podcast script from NDMO data");
  }
}

/**
 * Saves a binary file to the public directory
 */
function saveBinaryFile(fileName: string, content: Buffer): string {
  const publicDir = path.join(process.cwd(), 'public', 'podcasts');
  
  // Create the directory if it doesn't exist
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true, mode: 0o755 });
  }
  
  const filePath = path.join(publicDir, fileName);
  fs.writeFileSync(filePath, content, 'utf8');
  
  // Return the public URL
  return `/podcasts/${fileName}`;
}

/**
 * Parses the MIME type to extract WAV conversion options
 */
function parseMimeType(mimeType: string) {
  const [fileType, ...params] = mimeType.split(';').map(s => s.trim());
  const [_, format] = fileType.split('/');

  const options: {
    numChannels: number;
    sampleRate?: number;
    bitsPerSample?: number;
  } = {
    numChannels: 1,
  };

  if (format && format.startsWith('L')) {
    const bits = parseInt(format.slice(1), 10);
    if (!isNaN(bits)) {
      options.bitsPerSample = bits;
    }
  }

  for (const param of params) {
    const [key, value] = param.split('=').map(s => s.trim());
    if (key === 'rate') {
      options.sampleRate = parseInt(value, 10);
    }
  }

  return options as { numChannels: number; sampleRate: number; bitsPerSample: number };
}

/**
 * Creates a WAV header for audio data
 */
function createWavHeader(dataLength: number, options: { numChannels: number; sampleRate: number; bitsPerSample: number }) {
  const {
    numChannels,
    sampleRate,
    bitsPerSample,
  } = options;

  // http://soundfile.sapp.org/doc/WaveFormat
  const byteRate = sampleRate * numChannels * bitsPerSample / 8;
  const blockAlign = numChannels * bitsPerSample / 8;
  const buffer = Buffer.alloc(44);

  buffer.write('RIFF', 0);                      // ChunkID
  buffer.writeUInt32LE(36 + dataLength, 4);     // ChunkSize
  buffer.write('WAVE', 8);                      // Format
  buffer.write('fmt ', 12);                     // Subchunk1ID
  buffer.writeUInt32LE(16, 16);                 // Subchunk1Size (PCM)
  buffer.writeUInt16LE(1, 20);                  // AudioFormat (1 = PCM)
  buffer.writeUInt16LE(numChannels, 22);        // NumChannels
  buffer.writeUInt32LE(sampleRate, 24);         // SampleRate
  buffer.writeUInt32LE(byteRate, 28);           // ByteRate
  buffer.writeUInt16LE(blockAlign, 32);         // BlockAlign
  buffer.writeUInt16LE(bitsPerSample, 34);      // BitsPerSample
  buffer.write('data', 36);                     // Subchunk2ID
  buffer.writeUInt32LE(dataLength, 40);         // Subchunk2Size

  return buffer;
}

/**
 * Converts raw audio data to WAV format
 */
function convertToWav(rawData: string, mimeType: string) {
  const options = parseMimeType(mimeType);
  const buffer = Buffer.from(rawData, 'base64');
  const wavHeader = createWavHeader(buffer.length, options);

  return Buffer.concat([wavHeader, buffer]);
}

/**
 * Saves a text file to the public directory
 */
function saveTextFile(fileName: string, content: string): string {
  const publicDir = path.join(process.cwd(), 'public', 'podcasts');
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  
  const filePath = path.join(publicDir, fileName);
  fs.writeFileSync(filePath, content, 'utf8');
  
  // Return the public URL
  return `/podcasts/${fileName}`;
}

/**
 * Generates a podcast from NDMO data using Google Gemini AI
 */
export async function POST(request: NextRequest) {
  try {
    // Get request body for script or language preference
    const body = await request.json();
    const { script, lang = 'en' } = body;
    const isRTL = lang === 'ar';
    
    // Check if API key is available
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: "GEMINI_API_KEY environment variable is not set" },
        { status: 500 }
      );
    }
    
    // Create the script from NDMO data or use provided script
    const podcastScript = await createPodcastScript(isRTL, script);
    
    // Initialize Google Gemini AI
    const ai = new GoogleGenerativeAI(apiKey);
    
    // Get the model
    const model = ai.getGenerativeModel({
      model: 'gemini-pro',
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8000,
      }
    });
    
    const contents = [{
      role: 'user',
      parts: [{ text: `Read aloud in a warm, welcoming tone\n${podcastScript}` }]
    }];

    // Generate content from Gemini
    const response = await model.generateContent({
      contents
    });

    // Generate a filename with timestamp and language
    const timestamp = new Date().getTime();
    const fileName = `ndmo-podcast-${lang}-${timestamp}.txt`;
    
    // Get the response text
    const textResponse = response.response.text();
    
    // Save the text response as a file
    const audioUrl = saveTextFile(fileName, textResponse);
    
    // Return the file URL and name
    return NextResponse.json({
      success: true,
      url: audioUrl,
      fileName,
      language: lang
    });
    
  } catch (error) {
    console.error("Error generating podcast:", error);
    return NextResponse.json(
      { error: "Failed to generate podcast", details: (error as Error).message },
      { status: 500 }
    );
  }
}
