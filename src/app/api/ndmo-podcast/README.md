# NDMO Podcast Generator API

## Overview
This API converts NDMO (National Data Management Office) data into audio podcasts using Google's Gemini AI. The feature allows users to listen to structured NDMO data in a conversational podcast format, supporting both English and Arabic languages.

## Implementation Details

### API Endpoint
- **Route**: `/api/ndmo-podcast`
- **Method**: GET
- **Query Parameters**: 
  - `lang`: Language code ('en' or 'ar')

### Key Components
1. **Script Generation**: Converts structured NDMO data into a conversational script
2. **Text-to-Speech**: Uses Gemini AI's multi-speaker TTS capabilities to convert the script to audio
3. **File Storage**: Saves generated audio files to the `/public/podcasts` directory

### Data Flow
1. Fetches domains, controls, and specifications from Firestore
2. Constructs a conversational script introducing the NDMO data
3. Sends the script to Gemini AI for processing
4. Receives audio data from Gemini AI and saves it to a file
5. Returns the file URL to the client

## Required Dependencies
- `@google/genai`: Google Generative AI SDK
- `mime`: MIME type handling

## Configuration
This feature requires a Gemini API key from Google AI Studio. The key should be stored in the environment variables:
```
GEMINI_API_KEY=your_gemini_api_key_here
```

## Usage
Add the `<NDMOPodcast>` component to any page to provide podcast generation capabilities:
```tsx
import { NDMOPodcast } from "@/components/ui/NDMOPodcast";

// In your component
<NDMOPodcast lang={lang} />
```

## Security Considerations
- The API key is stored in environment variables and not exposed to clients
- Audio files are stored in the public directory but with timestamp-based filenames for security

## Future Improvements
- Add caching to avoid regenerating the same podcast multiple times
- Implement podcast duration limits for very large datasets
- Add additional voice customization options
