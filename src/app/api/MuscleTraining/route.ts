import { google } from '@ai-sdk/google';
import { streamText } from 'ai';
import { Ratelimit } from '@upstash/ratelimit';
import { kv } from '@vercel/kv';
import { NextRequest, NextResponse } from 'next/server';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Ensure Vercel KV environment variables are set (Vercel usually injects these)
if (!process.env.KV_URL) {
  console.warn('KV_URL environment variable not found. Rate limiting may not work as expected.');
}

let ratelimit: Ratelimit | null = null;

// Initialize rate limiter only if KV is configured
if (process.env.KV_URL) {
  ratelimit = new Ratelimit({
    redis: kv,
    // Limit to 5 requests per 60 seconds per IP
    limiter: Ratelimit.fixedWindow(5, '60s'),
    analytics: true, // Enable analytics
    prefix: '@upstash/ratelimit', // Default prefix
  });
} else {
  console.warn("Rate limiting disabled: Vercel KV environment variables not found.");
}

// Helper function to get IP address from request headers
function getIpAddress(req: NextRequest): string {
  // Try standard headers first
  let ip = req.headers.get('x-forwarded-for')?.split(',')[0].trim();
  if (ip) return ip;

  ip = req.headers.get('x-real-ip')?.trim();
  if (ip) return ip;

  // Fallback for local development or direct connections
  return '127.0.0.1'; // Default fallback
}

export async function POST(req: NextRequest) {
  const ip = getIpAddress(req); // Use helper function to get IP

  // Apply rate limiting if configured
  if (ratelimit) {
    try {
      const { success, limit, remaining, reset } = await ratelimit.limit(`ratelimit_muscle_training_${ip}`);
      console.log('Rate limit check:', { success, limit, remaining, reset, ip });

      // Add rate limit headers for client-side feedback
      const headers = new Headers();
      headers.set('X-RateLimit-Limit', limit.toString());
      headers.set('X-RateLimit-Remaining', remaining.toString());
      headers.set('X-RateLimit-Reset', reset.toString());

      if (!success) {
        return new NextResponse('Too many requests. Please try again later.', {
          status: 429,
          headers: headers
        });
      }
    } catch (error) {
        console.error("Rate limiting error:", error);
        return new NextResponse('Internal Server Error during rate limiting.', { status: 500 });
    }
  } else {
     console.log("Skipping rate limiting as it's not configured.");
  }

  try {
    // Extract muscle name from request body
    const { muscle } = await req.json();

    if (!muscle || typeof muscle !== 'string') {
      return new NextResponse('Muscle name is required in the request body.', { status: 400 });
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
       return new NextResponse('Server configuration error: Missing API Key.', { status: 500 });
    }

    // Get the AI model
    const model = google('gemini-2.5-flash-preview-04-17'); // Using a stable, documented model

    // Construct the prompt
    const prompt = `
    Generate a structured workout routine for training the ${muscle} muscle. 
    Include the following sections:
    1. 4-5 effective exercises targeting this muscle group
    2. Recommended sets and reps for each exercise
    3. Brief form tips for proper execution
    4. A suggested training frequency for this muscle
    
    Format the response in a clear, concise manner that's easy to read. Keep it informative but brief - suitable for display on a mobile device.
    `;

    // Call the AI model to generate text stream
    const result = await streamText({
      model: model,
      prompt: prompt,
    });

    // Respond with the stream using the correct method
    return result.toDataStreamResponse();

  } catch (error) {
    console.error('Error generating muscle training routine:', error);
    // Provide a generic error response
    return new NextResponse('Failed to generate training routine.', { status: 500 });
  }
} 