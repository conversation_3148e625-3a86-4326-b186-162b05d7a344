import { NextRequest } from 'next/server';
// Use require to avoid TypeScript issues with the Google Generative AI SDK
// @ts-ignore
const { GoogleGenerativeAI } = require('@google/generative-ai');

// Implementation of StreamingTextResponse since the ai package import is giving an error
class StreamingTextResponse extends Response {
  constructor(stream: ReadableStream) {
    super(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'X-Content-Type-Options': 'nosniff',
      },
    });
  }
}
import { 
  getAllDomains, 
  getControlsByDomain, 
  getSpecificationsByControl 
} from '@/Firebase/firestore/services/NDMOService';

/**
 * Supported language options for script generation
 */
type LanguageOption = 'en' | 'ar' | 'ar-eg';

/**
 * Script generation prompt templates for different languages
 */
const promptTemplates = {
  'en': `You are creating a podcast script about National Data Management Office (NDMO) data. 
  Write a conversational script between two speakers (Speaker 1 and Speaker 2) discussing the following NDMO data.
  Keep a professional but engaging tone. The script should be educational and highlight key data points.
  
  Include introduction, main content discussing the domains, controls and specifications, and a conclusion.
  Format the script with clear speaker indicators (Speaker 1: or Speaker 2:) at the beginning of each line.
  
  NDMO Data to discuss:
  {data}`,
  
  'ar': `أنت تقوم بإنشاء نص بودكاست حول بيانات المركز الوطني لإدارة البيانات (NDMO).
  اكتب نصًا حواريًا بين متحدثين (المتحدث 1 والمتحدث 2) يناقشان بيانات NDMO التالية.
  حافظ على نبرة احترافية ولكن جذابة. يجب أن يكون النص تعليميًا ويسلط الضوء على نقاط البيانات الرئيسية.
  
  قم بتضمين مقدمة، ومحتوى رئيسي يناقش المجالات والضوابط والمواصفات، وخاتمة.
  قم بتنسيق النص مع مؤشرات واضحة للمتحدث (المتحدث 1: أو المتحدث 2:) في بداية كل سطر.
  
  بيانات NDMO للمناقشة:
  {data}`,
  
  'ar-eg': `انت بتعمل سكريبت بودكاست عن بيانات المركز الوطني لإدارة البيانات (NDMO).
  اكتب حوار بين اتنين متحدثين (المتحدث 1 والمتحدث 2) بيناقشوا البيانات دي.
  خلي اللهجة محترمة بس كمان مشوقة. السكريبت لازم يكون تعليمي وبيركز على أهم النقاط في البيانات.
  
  شوف تحط مقدمة، وبعدين المحتوى الرئيسي اللي بيناقش المجالات والضوابط والمواصفات، وفي الآخر خاتمة.
  نسق السكريبت بحيث يكون فيه علامة واضحة للمتحدث (المتحدث 1: أو المتحدث 2:) في بداية كل سطر.
  
  بيانات NDMO اللي هتتناقش:
  {data}`
};

/**
 * Prepares NDMO data for inclusion in the prompt
 */
async function prepareNDMOData(lang: LanguageOption) {
  try {
    // Fetch all domains
    const domains = await getAllDomains();
    
    // Prepare text based on language
    const isArabic = lang === 'ar' || lang === 'ar-eg';
    
    let dataText = '';
    
    // Add domains section
    if (isArabic) {
      dataText += `المجالات (${domains.length}):\n`;
    } else {
      dataText += `Domains (${domains.length}):\n`;
    }
    
    // Process up to 3 domains for brevity
    for (let i = 0; i < Math.min(domains.length, 3); i++) {
      const domain = domains[i];
      dataText += `- ${domain.name} (ID: ${domain.id}), `;
      
      if (isArabic) {
        dataText += `يحتوي على ${domain.totalControls} ضوابط و ${domain.totalSpecs} مواصفات\n`;
      } else {
        dataText += `contains ${domain.totalControls} controls and ${domain.totalSpecs} specifications\n`;
      }
      
      // Fetch controls for this domain
      const controls = await getControlsByDomain(domain.id);
      
      // Add controls section (only for first domain to keep it manageable)
      if (i === 0 && controls.length > 0) {
        if (isArabic) {
          dataText += `  الضوابط في ${domain.name}:\n`;
        } else {
          dataText += `  Controls in ${domain.name}:\n`;
        }
        
        // Process up to 2 controls
        for (let j = 0; j < Math.min(controls.length, 2); j++) {
          const control = controls[j];
          dataText += `  - ${control.name} (ID: ${control.id}), `;
          
          if (isArabic) {
            dataText += `يحتوي على ${control.totalSpecs} مواصفات\n`;
          } else {
            dataText += `contains ${control.totalSpecs} specifications\n`;
          }
          
          // Fetch specifications for this control
          const specs = await getSpecificationsByControl(domain.id, control.id);
          
          // Add specifications section (only for first control)
          if (j === 0 && specs.length > 0) {
            if (isArabic) {
              dataText += `    المواصفات في ${control.name}:\n`;
            } else {
              dataText += `    Specifications in ${control.name}:\n`;
            }
            
            // Process up to 2 specifications
            for (let k = 0; k < Math.min(specs.length, 2); k++) {
              const spec = specs[k];
              dataText += `    - ${spec.name} (ID: ${spec.id}), `;
              
              if (isArabic) {
                dataText += `أولوية: ${spec.priority}\n`;
              } else {
                dataText += `Priority: ${spec.priority}\n`;
              }
            }
          }
        }
      }
    }
    
    return dataText;
  } catch (error) {
    console.error('Error preparing NDMO data:', error);
    throw new Error('Failed to prepare NDMO data for script generation');
  }
}

/**
 * API route handler for NDMO script generation
 */
export async function POST(request: NextRequest) {
  try {
    // Get request body
    const body = await request.json();
    const { language = 'en' } = body as { language: LanguageOption };
    
    // Validate language option
    if (!['en', 'ar', 'ar-eg'].includes(language)) {
      throw new Error('Invalid language option. Use "en", "ar", or "ar-eg"');
    }
    
    // Prepare NDMO data
    const ndmoData = await prepareNDMOData(language);
    
    // Get prompt template for the selected language
    const promptTemplate = promptTemplates[language];
    const prompt = promptTemplate.replace('{data}', ndmoData);
    
    // Check if API key is available
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }
    
    // Initialize Google AI
    const googleAI = new GoogleGenerativeAI(apiKey);
    
    // For streaming, we need to use the generateContentStream method
    const model = googleAI.getGenerativeModel({
      model: 'gemini-2.5-flash-preview-05-20'
    });
    
    // Create a properly formatted content request for Gemini
    const result = await model.generateContentStream({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 8000,
      }
    });
    
    // Convert the streaming response to a format compatible with StreamingTextResponse
    const stream = new ReadableStream({
      async start(controller) {
        for await (const chunk of result.stream) {
          const text = chunk.text();
          if (text) {
            controller.enqueue(new TextEncoder().encode(text));
          }
        }
        controller.close();
      }
    });
    
    // Return a streaming response
    return new StreamingTextResponse(stream);
    
  } catch (error) {
    console.error('Error generating script:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to generate script',
      details: (error as Error).message 
    }), { 
      status: 500, 
      headers: { 'Content-Type': 'application/json' } 
    });
  }
}
