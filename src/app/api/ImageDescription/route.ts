import { google } from '@ai-sdk/google';
import { generateText } from 'ai'; // Using generateText as streaming complex inputs like files can be tricky
import { Ratelimit } from '@upstash/ratelimit';
import { kv } from '@vercel/kv';
import { NextRequest, NextResponse } from 'next/server';

// Allow processing up to 60 seconds for potential image analysis
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}
if (!process.env.KV_URL) {
  console.warn('KV_URL environment variable not found. Rate limiting may not work as expected.');
}

let ratelimit: Ratelimit | null = null;

// Initialize rate limiter only if KV is configured
if (process.env.KV_URL) {
  ratelimit = new Ratelimit({
    redis: kv,
    limiter: Ratelimit.fixedWindow(3, '60s'), // Limit to 3 image descriptions per minute per IP
    analytics: true,
    prefix: '@upstash/ratelimit',
  });
} else {
  console.warn("Rate limiting disabled: Vercel KV environment variables not found.");
}

// Helper function to get IP address from request headers
function getIpAddress(req: NextRequest): string {
  let ip = req.headers.get('x-forwarded-for')?.split(',')[0].trim();
  if (ip) return ip;
  ip = req.headers.get('x-real-ip')?.trim();
  if (ip) return ip;
  return '127.0.0.1'; // Default fallback
}

export async function POST(req: NextRequest) {
  const ip = getIpAddress(req);

  // Apply rate limiting
  if (ratelimit) {
    try {
      const { success, limit, remaining, reset } = await ratelimit.limit(`ratelimit_image_description_${ip}`);
      console.log('Rate limit check:', { success, limit, remaining, reset, ip });

      const headers = new Headers();
      headers.set('X-RateLimit-Limit', limit.toString());
      headers.set('X-RateLimit-Remaining', remaining.toString());
      headers.set('X-RateLimit-Reset', reset.toString());

      if (!success) {
        return new NextResponse('Too many image description requests. Please try again later.', {
          status: 429,
          headers: headers
        });
      }
    } catch (error) {
      console.error("Rate limiting error:", error);
      return new NextResponse('Internal Server Error during rate limiting.', { status: 500 });
    }
  } else {
    console.log("Skipping rate limiting as it's not configured.");
  }

  try {
    // Extract image data from FormData
    const formData = await req.formData();
    const imageFile = formData.get('image') as File | null;

    if (!imageFile) {
      return new NextResponse('Image file is required in the request body.', { status: 400 });
    }

    // Check if the file is an image
    if (!imageFile.type.startsWith('image/')) {
        return new NextResponse('Invalid file type. Please upload an image.', { status: 400 });
    }

     // Limit file size (e.g., 10MB)
    const maxSize = 10 * 1024 * 1024; // 10 MB
    if (imageFile.size > maxSize) {
        return new NextResponse(`File size exceeds the limit of ${maxSize / (1024 * 1024)}MB.`, { status: 413 }); // 413 Payload Too Large
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return new NextResponse('Server configuration error: Missing API Key.', { status: 500 });
    }

    // Convert the File to Buffer/Uint8Array for the AI SDK
    const imageBuffer = Buffer.from(await imageFile.arrayBuffer());

    // Use a model that supports image input (vision capabilities)
    // gemini-1.5-flash is a good balance of capability and speed
    const model = google('gemini-1.5-flash');

    // Call the AI model
    const result = await generateText({
      model: model,
      messages: [
        {
          role: 'user',
          content: [
            { type: 'text', text: 'Describe this image in detail, focusing on the main subject and notable elements.' },
            {
              type: 'image',
              image: imageBuffer,
              mimeType: imageFile.type, // Pass the MIME type
            },
          ],
        },
      ],
      // Optional safety settings if needed
      // safetySettings: [ ... ]
    });

    // Respond with the generated text
    return NextResponse.json({ description: result.text });

  } catch (error: unknown) {
    console.error('Error generating image description:', error);
     let errorMessage = 'Failed to generate image description.';
     let statusCode = 500;

     if (error instanceof Error) {
        // Check for specific API errors if possible, otherwise use a generic message
        errorMessage = `Failed to generate image description: ${error.message}`;
     }
     // Handle potential fetch errors related to file size limits from Next.js itself
     // (Although we added a manual check, edge cases might exist)
     if (typeof error === 'object' && error !== null && 'type' in error && error.type === 'entity.too.large') {
        errorMessage = `File size exceeds the server limit.`;
        statusCode = 413; // Payload Too Large
     }


    return new NextResponse(errorMessage, { status: statusCode });
  }
} 